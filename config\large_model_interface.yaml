# =============================================================================
# 大模型接口配置文件 (Large Model Interface Configuration)
# =============================================================================

# -----------------------------------------------------------------------------
# 大模型设置 (Large Language Models Settings)
# -----------------------------------------------------------------------------
## 在线大模型 (Online Large Language Models)
# 通义千问平台配置 (Tongyi Platform Configuration)
tongyi_api_key: "sk-0ed0194c592241faa86bdf45131bf689"  # API密钥
tongyi_base_url: "https://dashscope.aliyuncs.com/compatible-mode/v1"  # 接口地址(无需修改)
tongyi_model: "qwen-vl-max-2025-04-08"  # 使用的模型，例如 "qwen-vl-max", "qwen-turbo"
tongyi_media_model: "flux-schnell"  # 通义千问文生图模型

# 讯飞星火平台配置 (iFlytek Spark Platform Configuration)
spark_app_id: "da2164e4"
spark_api_key: "4bb69b26d3984acf75a89b9a459fa055"
spark_api_secret: "MzFmMDBiMmMwYTY1Y2VkOTJmMDBlZjVh"
spark_model: "lite"  # 使用的模型版本(domain)，例如 "generalv3.5"
spark_model_url: "wss://spark-api.xf-yun.com/v1.1/chat" # Spark v3.5 的URL
spark_media_model: "image_understanding"  # 讯飞星火文生图模型

# 百度千帆平台配置 (Baidu Qianfan Platform Configuration)
# 请参考 https://cloud.baidu.com/doc/WENXINWORKSHOP/s/Um2wxbaps 获取您的API Key
qianfan_api_key: "bce-v3/ALTAK-BnYG0zhQ3GnJQvBZRzlV9/6739ac33116a0d3ab35f57078275bdfcb8034b48" # 请在此处填入您的API Key
qianfan_base_url: "https://qianfan.baidubce.com/v2" # 官方指定的OpenAI兼容接口地址
qianfan_model: "ernie-3.5-8k" # 使用的模型，例如 "ERNIE-Bot-4", "ERNIE-ViL-4.0"
qianfan_media_model: "ernie-vilg-v2"  # 百度千帆文生图模型

# OpenRouter平台配置 (OpenRouter Platform Configuration)
openrouter_api_key: "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"
openrouter_model: "moonshotai/kimi-k2:free" # 使用的模型，例如 "google/gemini-pro-vision"
openrouter_media_model: "stability-ai/stable-diffusion-3-5"  # OpenRouter文生图模型

## 离线大模型 (Offline Large Language Models)
# Ollama配置
ollama_host: "http://localhost:11434"  # Ollama服务器地址
ollama_model: "qwen2.5vl:7b"            # 默认Ollama模型，可修改为需要的模型

# -----------------------------------------------------------------------------
# 语音识别设置 (Speech Recognition Settings)
# -----------------------------------------------------------------------------
## 在线语音识别 (Online ASR)
oline_asr_model: 'paraformer-realtime-8k-v2'  # 可选：'paraformer-realtime-v2'、'paraformer-realtime-v1'等，使用的是通义千问平台的API
oline_asr_sample_rate: 16000      # ASR音频采样率

## 离线语音识别 (Offline ASR)
local_asr_model: "/home/<USER>/MODELS/asr/SenseVoiceSmall"  # 本地ASR模型路径

# -----------------------------------------------------------------------------
# 语音合成设置 (Text-to-Speech Settings)
# -----------------------------------------------------------------------------
## 通用TTS设置
tts_supplier: "baidu"  # TTS供应商，可选："aliyun"或"baidu"
tts_language: "en"     # 语言，"zh"：中文，"en"：英文

## 在线语音合成 (Online TTS)
# 阿里云通义千问语音合成配置
oline_tts_model: "cosyvoice-v2"  # 语音模型
voice_tone: "longwan_v2"         # 音色

# 百度智能云语音合成配置
baidu_API_KEY: 'lQ3ybx9UsPMCvpqZKpgkEa2q'        # 百度平台API密钥
baidu_SECRET_KEY: 'KBT3iWvMu1QXUVUL0CeNrKDhc1290Uo9'  # 百度平台SECRET密钥
CUID: 'IN7dAbz1thDJKVhLdypkpB6sDVG86xeG'         # 设备标识
PER: 4  # 发音人：基础音库(0=度小美, 1=度小宇, 3=度逍遥, 4=度丫丫)
SPD: 5  # 语速(0-15)，默认5
PIT: 5  # 音调(0-15)，默认5
VOL: 5  # 音量(0-9)，默认5

## 离线语音合成 (Offline TTS)
# 中文TTS模型
zh_tts_model: "/home/<USER>/MODELS/tts/zh/zh_CN-huayan-medium.onnx"
zh_tts_json: "/home/<USER>/MODELS/tts/zh/zh_CN-huayan-medium.onnx.json"

# 英文TTS模型
en_tts_model: "/home/<USER>/MODELS/tts/en/en_US-libritts-high.onnx"
en_tts_json: "/home/<USER>/MODELS/tts/en/en_US-libritts-high.onnx.json"
