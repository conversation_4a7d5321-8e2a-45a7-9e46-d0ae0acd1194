"""
工具适配器
将现有工具包装成标准化接口
"""

from typing import Dict, Any, List
from utils.tool_chain_manager import ToolInterface, ToolInput, ToolOutput
import os

class SeeWhatToolAdapter(ToolInterface):
    """seewhat工具适配器"""
    
    def __init__(self, tools_manager):
        self.tools_manager = tools_manager
    
    @property
    def tool_name(self) -> str:
        return "seewhat"
    
    @property
    def input_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {},
            "required": []
        }
    
    @property
    def output_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "description": {"type": "string"},
                "image_path": {"type": "string"}
            }
        }
    
    def execute(self, tool_input: ToolInput) -> ToolOutput:
        try:
            # ✅ 直接调用tools_manager的seewhat方法，它已经包含了完整的逻辑
            description = self.tools_manager.seewhat()

            if description:
                return ToolOutput(
                    tool_name=self.tool_name,
                    success=True,
                    data=description,
                    metadata={"analysis_type": "environment"}
                )
            else:
                return ToolOutput(
                    tool_name=self.tool_name,
                    success=False,
                    data=None,
                    metadata={},
                    error_message="Failed to capture image or analyze"
                )
        except Exception as e:
            return ToolOutput(
                tool_name=self.tool_name,
                success=False,
                data=None,
                metadata={},
                error_message=str(e)
            )

class AnalyzeVideoToolAdapter(ToolInterface):
    """analyze_video工具适配器"""
    
    def __init__(self, tools_manager):
        self.tools_manager = tools_manager
    
    @property
    def tool_name(self) -> str:
        return "analyze_video"
    
    @property
    def input_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "video_path": {
                    "type": "string",
                    "description": "Full path to the video file to be analyzed"
                }
            },
            "required": []  # 🔧 改为可选，支持自动使用默认视频
        }
    
    @property
    def output_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "description": {"type": "string"},
                "video_path": {"type": "string"}
            }
        }
    
    def execute(self, tool_input: ToolInput) -> ToolOutput:
        try:
            video_path = tool_input.arguments.get("video_path", "default")
            
            # 处理默认路径 - 简化占位符检测
            if not video_path or video_path in ["default", "test_video"]:
                default_path = os.path.join(
                    self.tools_manager.node.pkg_path,
                    "resources_file", "analyze_video", "test_video.mp4"
                )
                if os.path.exists(default_path):
                    video_path = default_path
                else:
                    return ToolOutput(
                        tool_name=self.tool_name,
                        success=False,
                        data=None,
                        metadata={},
                        error_message=f"Default test video not found: {default_path}"
                    )
            
            if video_path and os.path.exists(video_path):
                result = self.tools_manager.node.model_client.infer_with_video(
                    video_path, ""  # 让LLM自己决定如何分析视频
                )
                
                # 提取描述文本
                if isinstance(result, dict):
                    description = result.get('response', '')
                else:
                    description = str(result)
                
                return ToolOutput(
                    tool_name=self.tool_name,
                    success=True,
                    data=description,
                    metadata={"video_path": video_path, "analysis_type": "video_content"}
                )
            else:
                return ToolOutput(
                    tool_name=self.tool_name,
                    success=False,
                    data=None,
                    metadata={},
                    error_message=f"Video file not found: {video_path}"
                )
        except Exception as e:
            return ToolOutput(
                tool_name=self.tool_name,
                success=False,
                data=None,
                metadata={},
                error_message=str(e)
            )

class WriteDocumentToolAdapter(ToolInterface):
    """write_document工具适配器"""
    
    def __init__(self, tools_manager):
        self.tools_manager = tools_manager
    
    @property
    def tool_name(self) -> str:
        return "write_document"
    
    @property
    def input_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "filename": {
                    "type": "string",
                    "description": "Filename with extension (optional, auto-generated if not provided)"
                },
                "content": {
                    "type": "string",
                    "description": "Text content for creating documents, articles, poems, stories, reports, or any written content. ⚠️ IMPORTANT: This tool ONLY creates text/document content. It CANNOT and should NOT be used for image creation, artwork, or visual content. For any visual content, use generate_image tool instead."
                },
                "format": {
                    "type": "string",
                    "description": "Document format: md, txt, html, json",
                    "enum": ["md", "txt", "html", "json"]
                },
                "title": {
                    "type": "string",
                    "description": "Title of the document"
                }
            },
            "required": []  # 🔧 改为可选，支持通用数据传递自动填充
        }
    
    @property
    def output_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "file_path": {"type": "string"},
                "content": {"type": "string"}
            }
        }
    
    def can_use_previous_output(self, previous_output: ToolOutput) -> bool:
        """可以使用观察类工具的输出"""
        return previous_output.tool_name in ["seewhat", "analyze_video", "scan_table"]
    
    def execute(self, tool_input: ToolInput) -> ToolOutput:
        try:
            # 🎯 智能内容增强：如果内容为空或过短，尝试从前置工具获取
            content = tool_input.arguments.get("content", "")
            if not content.strip() or len(content.strip()) < 20:
                enhanced_content = self._enhance_empty_content(tool_input)
                if enhanced_content:
                    tool_input.arguments["content"] = enhanced_content
                    self.tools_manager.node.get_logger().info(f"📝 智能填充文档内容")

            # 调用原有的write_document方法
            result_message = self.tools_manager.write_document(tool_input.arguments)
            
            # 提取文件路径
            file_path = None
            if "保存到:" in result_message:
                file_path = result_message.split("保存到:")[-1].strip()
            
            return ToolOutput(
                tool_name=self.tool_name,
                success=True,
                data=result_message,
                metadata={
                    "file_path": file_path,
                    "format": tool_input.arguments.get("format", "txt"),
                    "title": tool_input.arguments.get("title", "文档")
                }
            )
        except Exception as e:
            return ToolOutput(
                tool_name=self.tool_name,
                success=False,
                data=None,
                metadata={},
                error_message=str(e)
            )

    def _enhance_empty_content(self, tool_input: ToolInput) -> str:
        """智能填充空内容 - 简化版，减少硬编码模板"""
        # 从前置工具输出中获取内容，但不添加硬编码模板
        for output in tool_input.previous_outputs:
            if output.tool_name == "seewhat" and output.success:
                # 直接返回观察数据，让LLM自己组织格式
                return output.data

        return ""


class GenerateImageToolAdapter(ToolInterface):
    """generate_image工具适配器"""
    
    def __init__(self, tools_manager):
        self.tools_manager = tools_manager
    
    @property
    def tool_name(self) -> str:
        return "generate_image"
    
    @property
    def input_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "prompt": {
                    "type": "string",
                    "description": "Visual description for creating images, artwork, pictures, drawings, paintings, or any visual content. ⚠️ IMPORTANT: This tool ONLY creates visual/image content. It CANNOT and should NOT be used for text creation like poems, articles, stories, or documents. For any text-based content, use write_document tool instead."
                }
            },
            "required": ["prompt"],  # ✅ prompt是必需的，这样工具链管理器会自动填充
            # 🧠 智能提示：在schema级别就明确工具边界
            "x-smart-hints": {
                "purpose": "Visual content creation only",
                "cannot_do": ["generate text", "write poems", "create documents"],
                "use_instead": "For text content, use write_document tool"
            }
        }
    
    @property
    def output_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "image_urls": {"type": "array"},
                "status": {"type": "string"}
            }
        }
    
    def can_use_previous_output(self, previous_output: ToolOutput) -> bool:
        """可以使用观察类工具的输出来增强提示词"""
        return previous_output.tool_name in ["seewhat", "analyze_video"]
    
    def execute(self, tool_input: ToolInput) -> ToolOutput:
        try:
            # 🎯 智能提示增强：如果prompt过于简单，提供建议
            prompt = tool_input.arguments.get("prompt", "")
            if len(prompt.strip()) < 10:
                enhanced_prompt = self._enhance_simple_prompt(prompt, tool_input.previous_outputs)
                if enhanced_prompt != prompt:
                    tool_input.arguments["prompt"] = enhanced_prompt
                    self.tools_manager.node.get_logger().info(f"🎨 智能增强提示词: {prompt} → {enhanced_prompt}")

            # 调用原有的generate_image方法
            result = self.tools_manager.generate_image(tool_input.arguments)
            
            # 这里需要根据实际的generate_image返回值进行适配
            # 假设返回的是字符串消息
            return ToolOutput(
                tool_name=self.tool_name,
                success=True,
                data=result,
                metadata={"prompt": tool_input.arguments.get("prompt", "")}
            )
        except Exception as e:
            return ToolOutput(
                tool_name=self.tool_name,
                success=False,
                data=None,
                metadata={},
                error_message=str(e)
            )

    def _enhance_simple_prompt(self, prompt: str, previous_outputs: List[ToolOutput]) -> str:
        """智能增强简单的提示词 - 简化版，减少硬编码格式"""
        # 如果有前置的观察结果，简单结合使用，不强制格式
        for output in previous_outputs:
            if output.tool_name == "seewhat" and output.success:
                # 简单结合，让LLM自己决定如何使用上下文
                return f"{prompt} {output.data[:50]}"

        # 如果没有前置结果，返回原始prompt
        return prompt


class ScanTableToolAdapter(ToolInterface):
    """scan_table工具适配器"""
    
    def __init__(self, tools_manager):
        self.tools_manager = tools_manager
    
    @property
    def tool_name(self) -> str:
        return "scan_table"
    
    @property
    def input_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "image_path": {
                    "type": "string",
                    "description": "Path to image file containing table (can be auto-filled from previous image capture)"
                }
            },
            "required": []  # 🔧 改为可选，支持从seewhat自动获取图像路径
        }
    
    @property
    def output_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "table_content": {"type": "string"},
                "file_path": {"type": "string"}
            }
        }
    
    def can_use_previous_output(self, previous_output: ToolOutput) -> bool:
        """可以使用seewhat的图像输出"""
        return previous_output.tool_name == "seewhat"
    
    def execute(self, tool_input: ToolInput) -> ToolOutput:
        try:
            # 调用原有的scan_table方法
            self.tools_manager.scan_table(tool_input.arguments)
            
            # scan_table方法没有返回值，这里需要根据实际情况适配
            return ToolOutput(
                tool_name=self.tool_name,
                success=True,
                data="Table scanning completed",
                metadata={"image_path": tool_input.arguments.get("image_path", "")}
            )
        except Exception as e:
            return ToolOutput(
                tool_name=self.tool_name,
                success=False,
                data=None,
                metadata={},
                error_message=str(e)
            )


class AgentCallToolAdapter(ToolInterface):
    """agent_call工具适配器"""

    def __init__(self, tools_manager):
        self.tools_manager = tools_manager

    @property
    def tool_name(self) -> str:
        return "agent_call"

    @property
    def input_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "task": {
                    "type": "string",
                    "description": "Complete task description for AI Agent to plan and execute"
                }
            },
            "required": ["task"]
        }

    @property
    def output_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "success": {"type": "boolean"},
                "message": {"type": "string"},
                "generated_files": {
                    "type": "array",
                    "items": {"type": "string"}
                },
                "steps_executed": {"type": "integer"}
            }
        }

    def execute(self, tool_input: ToolInput) -> ToolOutput:
        try:
            task_description = tool_input.arguments.get("task", "")

            if not task_description:
                return ToolOutput(
                    tool_name=self.tool_name,
                    success=False,
                    data=None,
                    metadata={},
                    error_message="Missing 'task' argument for agent_call"
                )

            # 获取AI Agent实例
            if hasattr(self.tools_manager, 'node') and hasattr(self.tools_manager.node, 'ai_agent'):
                ai_agent = self.tools_manager.node.ai_agent

                # 执行AI Agent任务
                agent_result = ai_agent.execute_task(task_description)

                if agent_result and agent_result.get("success"):
                    # 简化反馈消息，减少硬编码格式
                    feedback_message = "AI Agent任务完成"
                    if agent_result.get("generated_files"):
                        file_count = len(agent_result.get("generated_files", []))
                        feedback_message += f"，生成了{file_count}个文件"

                    steps = agent_result.get('steps_executed', 0)
                    if steps > 0:
                        feedback_message += f"，执行了{steps}个步骤"

                    return ToolOutput(
                        tool_name=self.tool_name,
                        success=True,
                        data=feedback_message,
                        metadata={
                            "agent_result": agent_result,
                            "generated_files": agent_result.get("generated_files", []),
                            "steps_executed": agent_result.get("steps_executed", 0)
                        }
                    )
                else:
                    return ToolOutput(
                        tool_name=self.tool_name,
                        success=False,
                        data=None,
                        metadata={},
                        error_message="AI Agent任务执行失败"
                    )
            else:
                return ToolOutput(
                    tool_name=self.tool_name,
                    success=False,
                    data=None,
                    metadata={},
                    error_message="AI Agent not available"
                )

        except Exception as e:
            return ToolOutput(
                tool_name=self.tool_name,
                success=False,
                data=None,
                metadata={},
                error_message=str(e)
            )
