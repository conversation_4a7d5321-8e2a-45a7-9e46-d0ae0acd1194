import os
import cv2
import re
from utils.tool_chain_manager import <PERSON><PERSON><PERSON>hainManager
from utils.tool_adapters import (
    SeeWhatToolAdapter, AnalyzeVideoToolAdapter, WriteDocumentToolAdapter,
    GenerateImageToolAdapter, ScanTableToolAdapter, AgentCallToolAdapter
)
# 🗑️ 删除硬编码映射器的导入
# 所有数据传递现在通过通用机制处理

class ToolsManager:
    def __init__(self, node):
        """
        初始化工具管理器。
        :param node: 传入 LargeModelService 节点的实例，以便访问其属性和方法。
        """
        self.node = node

        # 初始化新的工具链管理器
        self.tool_chain_manager = ToolChainManager(logger=node.get_logger())
        self._setup_tool_chain()

    def _setup_tool_chain(self):
        """设置工具链和数据映射器"""
        # 注册工具适配器
        self.tool_chain_manager.register_tool(SeeWhatToolAdapter(self))
        self.tool_chain_manager.register_tool(AnalyzeVideoToolAdapter(self))
        self.tool_chain_manager.register_tool(WriteDocumentToolAdapter(self))
        self.tool_chain_manager.register_tool(GenerateImageToolAdapter(self))
        self.tool_chain_manager.register_tool(ScanTableToolAdapter(self))
        self.tool_chain_manager.register_tool(AgentCallToolAdapter(self))

        # ✅ 数据传递现在通过tool_chain_manager的智能机制自动处理
        # 无需手动设置数据映射器，_apply_universal_data_passing会自动匹配



    def execute_tool_chain(self, tool_calls):
        """
        使用新的工具链管理器执行多工具调用
        :param tool_calls: 工具调用列表
        :return: 工具输出列表
        """
        return self.tool_chain_manager.execute_tool_chain(tool_calls)

    def execute_single_tool_with_chain(self, tool_call):
        """
        使用工具链管理器执行单个工具（支持数据传递）
        :param tool_call: 工具调用信息
        :return: 工具输出
        """
        if isinstance(tool_call, dict):
            tool_calls = [tool_call]
        else:
            # 解析字符串格式的工具调用
            tool_calls = [{"name": tool_call, "arguments": {}}]

        outputs = self.tool_chain_manager.execute_tool_chain(tool_calls)
        return outputs[0] if outputs else None

    def execute_tool(self, tool_call, allow_follow_up_tools=False):
        """
        根据工具调用（字符串或字典），分派并执行相应的工具。
        :param tool_call: 工具调用信息
        :param allow_follow_up_tools: 是否允许后续工具调用（用于多工具联动和AI Agent）
        """
        if isinstance(tool_call, str):
            # 保持对旧版字符串格式的兼容
            if tool_call == "seewhat()":
                return self.seewhat()
            elif "analyze_video" in tool_call:
                return self.analyze_video_from_string(tool_call)
            elif "generate_image" in tool_call:
                return self.generate_image_from_string(tool_call)
            elif "visual_positioning" in tool_call:
                return self.visual_positioning_from_string(tool_call)
            elif "scan_table" in tool_call:
                return self.scan_table_from_string(tool_call)

            else:
                self.node.get_logger().warn(f"Unknown tool call string: {tool_call}")
                return None
        elif isinstance(tool_call, dict):
            # 处理新版字典格式
            tool_name = tool_call.get("name")
            arguments = tool_call.get("arguments", {})
            
            if tool_name == "seewhat":
                return self.seewhat()
            elif tool_name == "analyze_video":
                return self.analyze_video(arguments, allow_follow_up_tools)
            elif tool_name == "generate_image":
                return self.generate_image(arguments)
            elif tool_name == "visual_positioning":
                return self.visual_positioning(arguments)
            elif tool_name == "scan_table":
                return self.scan_table(arguments)
            elif tool_name == "write_document":
                return self.write_document(arguments)

            else:
                self.node.get_logger().warn(f"Unknown tool name in dict: {tool_name}")
                return None
        else:
            self.node.get_logger().error(f"Unsupported tool_call format: {type(tool_call)}")
            return None

    def seewhat(self):
        """
        执行 seewhat() 工具，捕捉摄像头画面并交由大模型分析。
        ✅ 修复缓存问题：使用独立的上下文避免历史图像分析影响
        """
        self.node.get_logger().info("Executing seewhat() tool")
        image_path = self.capture_frame()
        if image_path:
            if self.node.language == 'zh':
                feedback = "请用纯文本详细描述这张图片中你看到的内容，包括物体、场景、颜色、位置等细节。这是一张实时摄像头画面。重要：只需要文字描述，不要使用JSON格式，不要调用任何工具，不要使用结构化格式。"
            else:
                feedback = "Please describe in plain text what you see in this image, including objects, scenes, colors, positions and other details. This is a real-time camera feed. Important: Only provide text description, no JSON format, no tool calls, no structured format."

            # ✅ 使用独立的消息上下文，避免历史图像分析影响
            # 只保留系统提示，不包含之前的图像分析历史
            message_to_use = self.node.load_message_from_file()

            # 过滤掉之前的图像相关消息，只保留系统提示和最近的文本对话
            if message_to_use:
                filtered_messages = []
                for msg in message_to_use:
                    # 保留系统消息
                    if msg.get('role') == 'system':
                        filtered_messages.append(msg)
                    # 保留最近的用户文本消息（不包含图像）
                    elif (msg.get('role') == 'user' and
                          isinstance(msg.get('content'), str) and
                          '看到' not in msg.get('content', '') and
                          'see' not in msg.get('content', '').lower()):
                        filtered_messages.append(msg)

                # ✅ 强制使用纯文本描述的系统提示，避免工具调用
                if len(filtered_messages) <= 1:
                    # 使用专门的图像描述系统提示
                    filtered_messages = [{
                        "role": "system",
                        "content": "You are an image description assistant. Your only task is to describe what you see in images using natural language. Never use JSON format, never call tools, never use structured responses. Only provide clear, detailed descriptions in plain text."
                    }]

                message_to_use = filtered_messages
            else:
                # ✅ 即使有历史消息，也要确保系统提示正确
                message_to_use = [{
                    "role": "system",
                    "content": "You are an image description assistant. Your only task is to describe what you see in images using natural language. Never use JSON format, never call tools, never use structured responses. Only provide clear, detailed descriptions in plain text."
                }]

            self.node.get_logger().debug(f"Using filtered message context with {len(message_to_use)} messages")

            # ✅ 添加异常处理，确保图像分析失败时有明确的错误信息
            try:
                self.node.get_logger().info("开始调用大模型进行图像分析...")
                result = self.node.model_client.infer_with_image(image_path, feedback, message=message_to_use)
                self.node.get_logger().info(f"大模型图像分析完成，结果类型: {type(result)}")

                if result:
                    self.node.get_logger().debug(f"分析结果: {str(result)[:200]}...")
                else:
                    self.node.get_logger().warning("大模型返回了空结果")

            except Exception as e:
                error_msg = f"图像分析失败: {str(e)}"
                self.node.get_logger().error(error_msg)
                print(f"\n❌ {error_msg}\n")
                return error_msg

            # 🔧 提取场景描述，确保工具链智能参数传递能正常工作
            analysis_text = ""

            if isinstance(result, dict):
                response_content = result.get('response', '')

                # ✅ 检查是否返回了JSON格式或工具调用
                if (response_content.startswith('{') or '```json' in response_content or
                    'tools' in response_content or response_content.strip() == ""):

                    self.node.get_logger().warning("大模型返回了JSON格式或空内容，强制获取纯文本描述")
                    # 强制重新获取纯净的场景描述
                    analysis_text = self._get_actual_scene_description(image_path, message_to_use)

                    # 如果仍然获取失败，使用默认描述
                    if not analysis_text or analysis_text.startswith('{'):
                        analysis_text = "当前摄像头捕获的室内场景，包含办公环境和人员活动"
                        self.node.get_logger().warning("使用默认场景描述")
                else:
                    # 直接的场景描述
                    analysis_text = response_content
            else:
                analysis_text = str(result)

            # 在文字终端显示结果
            if analysis_text:
                self.node.get_logger().info(f"📸 环境观察结果: {analysis_text[:200]}...")

            # ✅ 保存消息历史
            if isinstance(result, dict):
                new_messages = result.get('messages')
                if new_messages:
                    self.node.save_message_to_file(new_messages)

            # ✅ 返回纯净的场景描述，让tool_chain_manager的智能参数传递机制自动处理
            return analysis_text
        else:
            error_msg = "Failed to capture camera frame"
            self.node.get_logger().error(error_msg)
            print(f"\n❌ {error_msg}\n")
            return None

    def _get_actual_scene_description(self, image_path, message_context=None):
        """
        获取实际的场景描述，用于工具链数据传递
        """
        try:
            # 使用强制的纯文本描述提示
            if self.node.language == 'zh':
                scene_prompt = "请用纯文本详细描述这张图片。包括：环境、人物、物体、颜色、光线。不要使用JSON、不要调用工具、不要其他格式，只要文字描述。"
            else:
                scene_prompt = "Please describe this image in plain text only. Include: environment, people, objects, colors, lighting. No JSON, no tools, no other formats, just text description."

            # ✅ 强制使用纯文本系统提示，忽略message_context以避免工具调用干扰
            simple_context = [{
                "role": "system",
                "content": "You are an image description assistant. You MUST respond with plain text descriptions only. Never use JSON format, never call tools, never use any structured format. Only provide natural language descriptions of what you see in images."
            }]

            self.node.get_logger().info("开始获取纯净的场景描述...")
            result = self.node.model_client.infer_with_image(image_path, scene_prompt, message=simple_context)

            if isinstance(result, dict):
                description = result.get('response', '')
                # 如果还是返回JSON格式，尝试提取描述性内容
                if description.startswith('{') or '```json' in description:
                    self.node.get_logger().warning("大模型仍返回JSON格式，尝试提取描述内容")
                    # 使用更简单的提示重试
                    simple_prompt = "描述这张图片中你看到的内容。"
                    retry_result = self.node.model_client.infer_with_image(image_path, simple_prompt, message=simple_context)
                    if isinstance(retry_result, dict):
                        description = retry_result.get('response', '无法获取场景描述')
                    else:
                        description = str(retry_result)

                self.node.get_logger().info(f"获取到场景描述: {description[:100]}...")
                return description
            else:
                return str(result)

        except Exception as e:
            self.node.get_logger().error(f"获取场景描述失败: {e}")
            return "无法获取场景描述"

    def analyze_video(self, args, allow_follow_up_tools=False):
        """
        执行 analyze_video() 工具，分析指定路径的视频。
        :param allow_follow_up_tools: 是否允许后续工具调用
        """
        self.node.get_logger().info(f"Executing analyze_video() tool with args: {args}")
        try:
            video_path = args.get("video_path") if isinstance(args, dict) else args

            # 🔧 如果没有指定路径或路径无效，使用默认测试视频
            if not video_path or video_path in ["", "default", "test_video", "path_to_video", "video_path", "视频路径", "path_to_your_video_file"]:
                # 单工具调用：只使用默认路径
                default_path = os.path.join(self.node.pkg_path, "resources_file", "analyze_video", "test_video.mp4")
                if os.path.exists(default_path):
                    video_path = default_path
                    self.node.get_logger().info(f"✅ 使用默认测试视频进行分析: {video_path}")
                else:
                    self.node.get_logger().error(f"❌ 默认测试视频不存在: {default_path}")
                    self.node.get_logger().error(f"请在 resources_file/analyze_video/ 目录下放置 test_video.mp4 文件")
                    return

            if video_path and os.path.exists(video_path):
                result = self.node.model_client.infer_with_video(video_path, "请详细描述这个视频的内容。")
                # 根据调用上下文决定是否允许后续工具调用
                self.node.process_model_result(result, from_seewhat=not allow_follow_up_tools)
            elif video_path:
                self.node.get_logger().error(f"❌ 视频文件未找到: {video_path}")
            else:
                self.node.get_logger().error(f"❌ 缺少video_path参数")
        except Exception as e:
            self.node.get_logger().error(f"Failed to execute analyze_video tool: {e}")

    # 🔧 DEPRECATED: 保留用于向下兼容，建议使用字典格式调用
    def analyze_video_from_string(self, tool_call):
        """
        旧版：从字符串中提取路径并分析视频。
        DEPRECATED: 建议使用 analyze_video({"video_path": path}) 字典格式
        """
        self.node.get_logger().warning("使用了已弃用的字符串格式工具调用，建议使用字典格式")
        self.node.get_logger().info(f"Executing analyze_video() tool with call: {tool_call}")
        try:
            # 从 "analyze_video('/path/to/video.mp4')" 中提取路径
            parts = tool_call.split("'")
            if len(parts) > 1:
                video_path = parts[1] # 正确提取路径字符串
                self.analyze_video({"video_path": video_path})
            else:
                self.node.get_logger().error(f"Could not extract path from tool call: {tool_call}")
        except Exception as e:
            self.node.get_logger().error(f"Failed to parse video path from tool call '{tool_call}': {e}")

    def generate_image(self, args):
        """
        执行 generate_image() 工具，生成图片。
        """
        self.node.get_logger().info(f"Executing generate_image() tool with args: {args}")
        try:
            prompt = args.get("prompt") if isinstance(args, dict) else args
            if not prompt:
                self.node.get_logger().error("Missing 'prompt' argument for generate_image.")
                return

            # 调用独立的文生图服务
            result = self.node.model_client.text_to_image(prompt)
            
            if result and result.get('status') == 'success':
                image_urls = result.get('image_urls', [])
                
                # 下载并保存图片
                saved_paths = []
                for i, url in enumerate(image_urls):
                    try:
                        import urllib.request
                        # 创建保存目录
                        save_dir = os.path.expanduser("~/yahboom_ws/src/largemodel/resources_file/generated_images")
                        os.makedirs(save_dir, exist_ok=True)
                        # 生成文件名
                        filename = f"image_{int(self.node.get_clock().now().nanoseconds / 1e6)}_{i}.jpg"
                        save_path = os.path.join(save_dir, filename)
                        # 下载图片
                        urllib.request.urlretrieve(url, save_path)
                        saved_paths.append(save_path)
                        self.node.get_logger().info(f"Image saved to: {save_path}")
                    except Exception as e:
                        self.node.get_logger().error(f"Failed to download image from {url}: {e}")
                
                if saved_paths:
                    feedback = f"我已经根据您的描述“{prompt}”生成了图片，并保存在了以下位置：{', '.join(saved_paths)}"
                else:
                    feedback = f"图片已生成，但下载失败。您可以在以下地址查看：{', '.join(image_urls)}"
            else:
                error_msg = result.get('error', '未知错误') if isinstance(result, dict) else str(result)
                feedback = f"抱歉，生成图片时遇到错误: {error_msg}"
            
            # 通过TTS将结果反馈给用户
            self.node.model_client.voice_synthesis(feedback, self.node.tts_out_path)
            self.node.play_audio_async(self.node.tts_out_path)

        except Exception as e:
            self.node.get_logger().error(f"Failed to execute generate_image tool: {e}")

    # 🔧 DEPRECATED: 保留用于向下兼容，建议使用字典格式调用
    def generate_image_from_string(self, tool_call):
        """
        旧版：从字符串中提取prompt并生成图片。
        DEPRECATED: 建议使用 generate_image({"prompt": prompt}) 字典格式
        """
        self.node.get_logger().warning("使用了已弃用的字符串格式工具调用，建议使用字典格式")
        try:
            parts = tool_call.split("'")
            if len(parts) > 1:
                prompt = parts[1]
                self.generate_image({"prompt": prompt})
            else:
                self.node.get_logger().error(f"Could not extract prompt from tool call string: {tool_call}")
        except IndexError:
            self.node.get_logger().error(f"Could not extract prompt from tool call string: {tool_call}")

    def capture_frame(self):
        """
        捕获一帧摄像头画面。这个函数是从 LargeModelService 迁移过来的。
        ✅ 修复缓存问题：每次都获取最新画面并使用时间戳命名
        """
        if not self.node.camera_initialized:
            if not self.node.init_camera_device():
                self.node.get_logger().error("No camera available")
                return None

        if self.node.cap is None:
            self.node.get_logger().error("Camera object is None, cannot capture frame.")
            self.node.camera_initialized = False
            return None

        # ✅ 彻底清空缓冲区，确保获取最新帧
        # 增加清理帧数，确保获取真正的实时画面
        self.node.get_logger().debug("Clearing camera buffer to get latest frame")
        for i in range(10):  # 增加到10帧，确保彻底清空缓冲区
            ret, frame = self.node.cap.read()
            if not ret:
                self.node.get_logger().warn(f"Failed to capture frame during buffer clearing at frame {i}")
                break
            # 添加短暂延迟，确保摄像头有时间更新
            import time
            time.sleep(0.01)  # 10ms延迟

        # 最终获取用于分析的帧
        ret, frame = self.node.cap.read()
        if not ret:
            self.node.get_logger().warn("Failed to capture final frame")
            return None

        self.node.get_logger().debug("Successfully captured fresh frame from camera")

        self.node.last_frame = frame.copy()

        # 🔧 修复图片显示更新问题
        cv2.imshow("Camera Feed", frame)
        cv2.waitKey(1)

        # 强制刷新窗口
        try:
            cv2.getWindowProperty("Camera Feed", cv2.WND_PROP_VISIBLE)
        except cv2.error:
            # 如果窗口不存在，重新创建
            cv2.namedWindow("Camera Feed", cv2.WINDOW_AUTOSIZE)

        resources_dir = os.path.join(self.node.pkg_path, "resources_file")
        os.makedirs(resources_dir, exist_ok=True)

        # ✅ 使用时间戳命名，避免文件缓存问题
        import time
        timestamp = int(time.time() * 1000)  # 毫秒级时间戳
        temp_image_path = os.path.join(resources_dir, f"captured_frame_{timestamp}.jpg")

        try:
            success = cv2.imwrite(temp_image_path, self.node.last_frame)
            if not success:
                self.node.get_logger().error(f"Failed to save frame to {temp_image_path}")
                return None

            # ✅ 验证图像内容，确保获取了新的画面
            frame_hash = self._calculate_frame_hash(self.node.last_frame)
            self.node.get_logger().info(f"Frame captured and saved to {temp_image_path}, hash: {frame_hash[:8]}")

            # ✅ 清理旧的图像文件，避免磁盘空间占用
            self._cleanup_old_frames(resources_dir)

        except Exception as e:
            self.node.get_logger().error(f"Exception while saving frame: {e}")
            return None

        return temp_image_path

    def _cleanup_old_frames(self, resources_dir):
        """
        清理旧的图像文件，只保留最近的5个文件
        """
        try:
            import glob
            import os

            # 查找所有captured_frame_*.jpg文件
            pattern = os.path.join(resources_dir, "captured_frame_*.jpg")
            frame_files = glob.glob(pattern)

            # 按修改时间排序，保留最新的5个文件
            if len(frame_files) > 5:
                frame_files.sort(key=os.path.getmtime)
                files_to_delete = frame_files[:-5]  # 除了最新的5个，其他都删除

                for file_path in files_to_delete:
                    try:
                        os.remove(file_path)
                        self.node.get_logger().debug(f"Cleaned up old frame: {file_path}")
                    except Exception as e:
                        self.node.get_logger().warning(f"Failed to delete old frame {file_path}: {e}")

        except Exception as e:
            self.node.get_logger().warning(f"Failed to cleanup old frames: {e}")

    def _calculate_frame_hash(self, frame):
        """
        计算图像帧的哈希值，用于验证图像内容是否发生变化
        """
        try:
            import hashlib
            # 将图像转换为字节串并计算MD5哈希
            frame_bytes = frame.tobytes()
            hash_md5 = hashlib.md5(frame_bytes).hexdigest()
            return hash_md5
        except Exception as e:
            self.node.get_logger().warning(f"Failed to calculate frame hash: {e}")
            return "unknown"

    def visual_positioning(self, args):
        """
        执行 visual_positioning() 工具，识别指定图片中某个物品的坐标位置，并将结果保存为MD文件。
        """
        self.node.get_logger().info(f"Executing visual_positioning() tool with args: {args}")
        try:
            # 获取参数
            image_path = args.get("image_path") if isinstance(args, dict) else None
            object_name = args.get("object_name") if isinstance(args, dict) else None

            # 🔧 如果没有指定路径或路径无效，使用默认测试图片
            if not image_path or image_path in ["", "default", "test_image", "current_image_path", "path_to_image", "image_path", "图片路径", "path_to_your_image"]:
                # 单工具调用：只使用默认路径
                default_path = os.path.join(self.node.pkg_path, "resources_file", "visual_positioning", "test_image.jpg")
                if os.path.exists(default_path):
                    image_path = default_path
                    self.node.get_logger().info(f"✅ 使用默认测试图片进行视觉定位: {image_path}")
                else:
                    self.node.get_logger().error(f"❌ 默认测试图片不存在: {default_path}")
                    self.node.get_logger().error(f"请在 resources_file/visual_positioning/ 目录下放置 test_image.jpg 文件")
                    return
            else:
                # 解析 ~ 符号为实际路径
                image_path = os.path.expanduser(image_path)

            if not image_path or not os.path.exists(image_path):
                self.node.get_logger().error(f"❌ 图片文件未找到: {image_path}")
                return
            
            if not object_name:
                self.node.get_logger().error("Missing 'object_name' argument for visual positioning.")
                return
            
            # 构造提示词，要求大模型识别指定物品的坐标位置
            if self.node.language == 'zh':
                prompt = f"请仔细分析这张图片，用一个个框定位图像每一个{object_name}的位置并描述其各自的特征。请为每一个{object_name}单独一行输出坐标，严格按照[中心点x,中心点y,宽度,高度]的格式，不要包含任何其他文字。例如：\n[100,200,50,80]\n[150,250,60,90]。请在坐标前添加对每个{object_name}的简要描述。"
            else:
                prompt = f"Please carefully analyze this image and find the position of all {object_name}. Output coordinates for each person on a separate line, strictly in the format [center_x,center_y,width,height]. Do not include any other text. For example:\n[100,200,50,80]\n[150,250,60,90]. Please add a brief description of each {object_name} before the coordinates."
            
            # 调用大模型进行视觉定位
            messages_to_use = self.node.load_message_from_file()
            result = self.node.model_client.infer_with_image(image_path, prompt, message=messages_to_use)
            
            # 处理结果 - 确保是字符串类型
            if isinstance(result, dict):
                response_text = result.get('response', '')
            else:
                response_text = str(result)  # 确保转换为字符串
            
            # 确保response_text是字符串
            if not isinstance(response_text, str):
                response_text = str(response_text)
            
            # 分离坐标和解释信息
            lines = response_text.split('\n')
            coordinates_lines = []
            explanation_lines = []
            
            # 提取坐标行
            for line in lines:
                line_str = str(line)  # 确保是字符串
                if re.search(r'\[\s*\d+\s*,\s*\d+\s*,\s*\d+\s*,\s*\d+\s*\]', line_str):
                    coordinates_lines.append(line_str)
                else:
                    explanation_lines.append(line_str)
            
            # 组织内容
            coordinates_content = "\n".join(coordinates_lines) if coordinates_lines else "未检测到有效坐标信息"
            explanation_content = "\n".join(explanation_lines) if explanation_lines else "无额外解释信息"
            
            # 保存结果为MD文件
            # 与图片保存在同一目录下
            image_dir = os.path.dirname(image_path)
            image_filename = os.path.basename(image_path)
            image_name_without_ext = os.path.splitext(image_filename)[0]
            md_filename = f"{image_name_without_ext}_position.md"
            md_file_path = os.path.join(image_dir, md_filename)
            
            # 确保所有内容都是字符串
            coordinates_content = str(coordinates_content)
            explanation_content = str(explanation_content)
            
            # 修改写入MD文件的部分，保存坐标和解释
            with open(md_file_path, 'w', encoding='utf-8') as f:
                f.write(f"# visual Positioning Rsult\n\n")
                f.write(f"Targe: {object_name}\n")
                f.write(f"Position: {coordinates_content}\n")
                if explanation_content.strip():
                    f.write(f"\n## explain\n\n")
                    f.write(f"{explanation_content}\n")
                f.write(f"\n[中心点x,中心点y,宽度,高度] \n[center_x,center_y,width,height]。\n")
            
            self.node.get_logger().info(f"Visual positioning result saved to: {md_file_path}")
            
        except Exception as e:
            self.node.get_logger().error(f"Failed to execute visual_positioning tool: {e}")

    # 🔧 DEPRECATED: 保留用于向下兼容，建议使用字典格式调用
    def visual_positioning_from_string(self, tool_call):
        """
        旧版：从字符串中提取参数并执行视觉定位。
        DEPRECATED: 建议使用 visual_positioning({"image_path": path, "object_name": name}) 字典格式
        """
        self.node.get_logger().warning("使用了已弃用的字符串格式工具调用，建议使用字典格式")
        try:
            # 解析字符串格式: visual_positioning('/path/to/image.jpg', 'object_name')
            # 提取路径和物品名称
            parts = tool_call.split("'")
            if len(parts) >= 3:
                image_path = parts[1]
                # 解析 ~ 符号为实际路径
                image_path = os.path.expanduser(image_path)
                object_name = parts[3] if len(parts) > 3 else ""
                self.visual_positioning({"image_path": image_path, "object_name": object_name})
            else:
                self.node.get_logger().error(f"Could not extract parameters from tool call string: {tool_call}")
        except Exception as e:
            self.node.get_logger().error(f"Failed to parse parameters from tool call '{tool_call}': {e}")

    # 🔧 DEPRECATED: 保留用于向下兼容，建议使用字典格式调用
    def scan_table_from_string(self, tool_call):
        """
        旧版：从字符串中提取参数并执行表格扫描。
        DEPRECATED: 建议使用 scan_table({"image_path": path}) 字典格式
        """
        self.node.get_logger().warning("使用了已弃用的字符串格式工具调用，建议使用字典格式")
        try:
            # 解析字符串格式: scan_table('/path/to/image.jpg')
            # 提取路径
            parts = tool_call.split("'")
            if len(parts) >= 2:
                image_path = parts[1]
                # 解析 ~ 符号为实际路径
                image_path = os.path.expanduser(image_path)
                self.scan_table({"image_path": image_path})
            else:
                self.node.get_logger().error(f"Could not extract parameters from tool call string: {tool_call}")
        except Exception as e:
            self.node.get_logger().error(f"Failed to parse parameters from tool call '{tool_call}': {e}")

    def scan_table(self, args):
        """
        执行 scan_table() 工具，识别图片中的表格内容，并将结果保存为MD文件。
        """
        self.node.get_logger().info(f"Executing scan_table() tool with args: {args}")
        try:
            # 获取参数
            image_path = args.get("image_path") if isinstance(args, dict) else None

            # 🔧 如果没有指定路径或路径无效，使用默认测试图片
            if not image_path or image_path in ["", "default", "test_table", "path_to_image", "image_path", "图片路径", "path_to_your_table_image"]:
                # 单工具调用：只使用默认路径
                default_path = os.path.join(self.node.pkg_path, "resources_file", "scan_table", "test_table.jpg")
                if os.path.exists(default_path):
                    image_path = default_path
                    self.node.get_logger().info(f"✅ 使用默认测试表格图片进行扫描: {image_path}")
                else:
                    self.node.get_logger().error(f"❌ 默认测试表格图片不存在: {default_path}")
                    self.node.get_logger().error(f"请在 resources_file/scan_table/ 目录下放置 test_table.jpg 文件")
                    return
            else:
                # 解析 ~ 符号为实际路径
                image_path = os.path.expanduser(image_path)
            
            # 构造提示词，要求大模型将图片中的表格转换为Markdown格式
            if self.node.language == 'zh':
                prompt = "请仔细分析这张图片，将图片中的表格内容完整地转换为Markdown格式。请确保格式正确，不要包含任何其他无关文字。"
            else:
                prompt = "Please carefully analyze this image and convert the table content into Markdown format. Ensure the format is correct and do not include any other irrelevant text."
            
            # 使用当前平台的多模态能力进行表格识别
            self.node.get_logger().info(f"使用 {self.node.model_client.llm_platform} 平台进行表格扫描")
            
            # 使用通用的多模态推理接口，支持所有平台
            try:
                # 调用通用的图像推理接口
                messages_to_use = self.node.load_message_from_file()
                result = self.node.model_client.infer_with_image(image_path, prompt, message=messages_to_use)

                # 处理结果 - 确保是字符串类型
                if isinstance(result, dict):
                    response_text = result.get('response', '')
                else:
                    response_text = str(result)

                # 确保response_text是字符串
                if not isinstance(response_text, str):
                    response_text = str(response_text)

            except Exception as e:
                self.node.get_logger().error(f"Failed to analyze table with {self.node.model_client.llm_platform}: {e}")
                return
            
            # 保存结果为MD文件
            image_dir = os.path.dirname(image_path)
            image_filename = os.path.basename(image_path)
            image_name_without_ext = os.path.splitext(image_filename)[0]
            md_filename = f"{image_name_without_ext}_table.md"
            md_file_path = os.path.join(image_dir, md_filename)
            
            with open(md_file_path, 'w', encoding='utf-8') as f:
                f.write(f"# Scanned Table Result\n\n")
                f.write(response_text)
            
            self.node.get_logger().info(f"Table scan result saved to: {md_file_path}")
            
        except Exception as e:
            self.node.get_logger().error(f"Failed to execute scan_table tool: {e}")

    def write_document(self, arguments):
        """
        通用文档生成工具
        :param arguments: 包含filename, content, format, title等参数的字典
        """
        try:
            filename = arguments.get("filename", "")
            content = arguments.get("content", "")
            format_type = arguments.get("format", "txt").lower()
            title = arguments.get("title", "文档")

            self.node.get_logger().info(f"开始生成{format_type.upper()}文档: {filename}")

            # 根据格式生成不同的文档内容 - 只做最基本的格式包装
            if format_type == "md":
                # MD格式：只添加标题和时间戳
                document_content = f"# {title}\n\n{content}\n\n---\n*生成时间: {self._get_current_time()}*"
                if not filename:
                    filename = f"{title.replace(' ', '_')}_{self._get_timestamp()}.md"
            elif format_type == "html":
                # HTML格式：只添加基本的HTML结构
                document_content = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{title}</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }}
        .footer {{ margin-top: 40px; font-size: 12px; color: #666; }}
    </style>
</head>
<body>
    {content}
    <div class="footer">生成时间: {self._get_current_time()}</div>
</body>
</html>"""
                if not filename:
                    filename = f"{title.replace(' ', '_')}_{self._get_timestamp()}.html"
            elif format_type == "json":
                # JSON格式：包装成JSON对象
                import json
                data = {
                    "title": title,
                    "content": content,
                    "generated_time": self._get_current_time(),
                    "timestamp": self._get_timestamp()
                }
                document_content = json.dumps(data, ensure_ascii=False, indent=2)
                if not filename:
                    filename = f"{title.replace(' ', '_')}_{self._get_timestamp()}.json"
            else:
                # 默认TXT格式：纯文本
                document_content = f"{title}\n{'=' * len(title)}\n\n{content}\n\n生成时间: {self._get_current_time()}"
                if not filename:
                    filename = f"{title.replace(' ', '_')}_{self._get_timestamp()}.txt"

            # 保存文件到专门的文档目录
            doc_dir = "/home/<USER>/yahboom_ws/src/largemodel/resources_file/documents"

            # 确保目录存在
            os.makedirs(doc_dir, exist_ok=True)

            doc_file_path = os.path.join(doc_dir, filename)

            with open(doc_file_path, 'w', encoding='utf-8') as f:
                f.write(document_content)

            self.node.get_logger().info(f"{format_type.upper()}文档已保存到: {doc_file_path}")
            return f"{format_type.upper()}格式的文档已生成并保存到: {doc_file_path}"

        except Exception as e:
            self.node.get_logger().error(f"生成文档失败: {e}")
            return f"生成文档失败: {str(e)}"

    def _get_current_time(self):
        """获取当前时间字符串"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    def _get_timestamp(self):
        """获取时间戳"""
        import time
        return str(int(time.time()))


