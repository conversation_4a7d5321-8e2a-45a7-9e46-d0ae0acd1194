# 🔧 清理后的导入语句 - 只保留实际使用的导入
import dashscope  # 用于通义千问API调用
from openai import OpenAI  # 用于OpenAI兼容的API调用
import os  # 用于文件路径操作
import piper  # 用于本地TTS
import wave  # 用于音频文件处理
from http import HTTPStatus  # 用于ASR状态检查
from dashscope.audio.asr import Recognition  # 用于在线ASR
from funasr import AutoModel  # 用于本地ASR模型
from dashscope.audio.tts_v2 import *  # 用于在线TTS
from dashscope.audio.asr import *  # 用于ASR相关功能
from ament_index_python.packages import get_package_share_directory  # 用于获取包路径
from ollama import Client  # 用于Ollama客户端
from utils.promot import get_prompt  # 用于获取提示词
import yaml  # 用于配置文件解析
import base64  # 用于图像编码
import requests  # 用于HTTP请求
import json  # 用于JSON处理
# 🔧 移除未使用的导入：Application, urlopen, Request, URLError, urlencode, quote_plus, logging, sys
from sparkai.llm.llm import ChatSparkLLM  # 用于讯飞星火
from sparkai.core.messages import ChatMessage  # 用于星火消息格式

class model_interface:
    def __init__(self, llm_platform='ollama'):
        self.llm_platform = llm_platform
        self.client = None
        self.init_config_param()

    def init_config_param(self):
        self.pkg_path=get_package_share_directory('largemodel')      
        config_param_file = os.path.join(self.pkg_path, "config", "large_model_interface.yaml")
        with open(config_param_file, 'r') as file:
            config_param = yaml.safe_load(file)
        
        # 通义千问配置
        self.tongyi_api_key =config_param.get('tongyi_api_key')
        self.tongyi_base_url=config_param.get('tongyi_base_url')
        self.tongyi_model = config_param.get('tongyi_model')
        self.tongyi_media_model = config_param.get('tongyi_media_model', 'wanx-v1')

        # 讯飞星火配置
        self.spark_app_id = config_param.get('spark_app_id')
        self.spark_api_key = config_param.get('spark_api_key')
        self.spark_api_secret = config_param.get('spark_api_secret')
        self.spark_model = config_param.get('spark_model')
        self.spark_model_url = config_param.get('spark_model_url')
        self.spark_media_model = config_param.get('spark_media_model', 'image_understanding')

        # 百度千帆配置
        self.qianfan_api_key = config_param.get('qianfan_api_key')
        self.qianfan_base_url = config_param.get('qianfan_base_url')
        self.qianfan_model = config_param.get('qianfan_model')
        self.qianfan_media_model = config_param.get('qianfan_media_model', 'ernie-vilg-v2')

        # OpenRouter配置
        self.openrouter_api_key = config_param.get('openrouter_api_key')
        self.openrouter_model = config_param.get('openrouter_model')

        # Ollama配置
        self.ollama_host = config_param.get('ollama_host', 'http://localhost:11434')
        self.ollama_model = config_param.get('ollama_model', 'llava')

        # ASR & TTS 配置
        self.oline_asr_model=config_param.get('oline_asr_model')
        self.zh_tts_model=config_param.get('zh_tts_model')
        self.zh_tts_json=config_param.get('zh_tts_json')
        self.en_tts_model=config_param.get('en_tts_model')
        self.en_tts_json=config_param.get('en_tts_json')
        self.oline_asr_sample_rate=config_param.get('oline_asr_sample_rate')
        self.oline_tts_model=config_param.get('oline_tts_model')
        self.voice_tone=config_param.get('voice_tone')
        self.local_asr_model=config_param.get('local_asr_model')
        self.tts_supplier=config_param.get('tts_supplier')
        self.baidu_API_KEY=config_param.get('baidu_API_KEY')
        self.baidu_SECRET_KEY=config_param.get('baidu_SECRET_KEY')
        self.CUID=config_param.get('CUID')
        self.PER=config_param.get('PER')
        self.SPD=config_param.get('SPD')
        self.PIT=config_param.get('PIT')
        self.VOL=config_param.get('VOL')

    def init_llm(self):
        """根据平台名称初始化对应的模型客户端"""
        if self.llm_platform == 'ollama':
            self.init_ollama()
        elif self.llm_platform == 'tongyi':
            self.init_tongyi()
        elif self.llm_platform == 'spark':
            self.init_spark()
        elif self.llm_platform == 'qianfan':
            self.init_qianfan()
        elif self.llm_platform == 'openrouter':
            self.init_openrouter()
        else:
            print(f"Unsupported LLM platform: {self.llm_platform}, defaulting to Ollama.")
            self.init_ollama()

    def init_language(self,language):
        self.system_text={}
        if language=='zh':
            self.system_text['text1']="请分析这个图像或视频"
            self.system_text['text2']="我已经准备好，请开始您的指令吧"        
        elif language=='en':
            self.system_text['text1']="Please analyze this image or video"        
            self.system_text['text2']="I am ready. Please start your instructions" 
        # 初始化消息历史
        self.init_messages()
        
    def init_messages(self, mcp_server=None, mcp_client=None):
        """通用消息历史初始化"""
        self.messages = [
            {"role": "system", "content": get_prompt(mcp_server, mcp_client)},
            {"role": "assistant", "content": self.system_text.get('text2', "I am ready.")}
        ]

    def init_ollama(self):
        """Initialize Ollama client"""
        try:
            self.client = Client(host=self.ollama_host)
            print(f"Ollama client initialized successfully. Using model {self.ollama_model}")
        except Exception as e:
            print(f"Failed to initialize Ollama client: {e}")
            self.client = None

    def init_tongyi(self):
        """Initialize Tongyi client"""
        try:
            dashscope.api_key = self.tongyi_api_key
            self.client = OpenAI(api_key=self.tongyi_api_key, base_url=self.tongyi_base_url)
            print(f"Tongyi (Dashscope) client initialized successfully. Using model {self.tongyi_model}")
        except Exception as e:
            print(f"Failed to initialize Tongyi client: {e}")
            self.client = None

    def init_spark(self):
        """Initialize Spark client"""
        try:
            self.client = ChatSparkLLM(
                spark_api_url=self.spark_model_url,
                spark_app_id=self.spark_app_id,
                spark_api_key=self.spark_api_key,
                spark_api_secret=self.spark_api_secret,
                spark_llm_domain=self.spark_model,
                streaming=False,
            )
            print(f"Spark client initialized successfully. Using domain {self.spark_model}")
        except Exception as e:
            print(f"Failed to initialize Spark client: {e}")
            self.client = None

    def init_qianfan(self):
        """Initialize Qianfan client using OpenAI compatible mode"""
        try:
            self.client = OpenAI(
                api_key=self.qianfan_api_key,
                base_url=self.qianfan_base_url
            )
            print(f"Qianfan client (OpenAI compatible) initialized successfully. Using model {self.qianfan_model}")
        except Exception as e:
            print(f"Failed to initialize Qianfan client: {e}")
            self.client = None

    def init_openrouter(self):
        """Initialize OpenRouter client"""
        try:
            self.client = OpenAI(
                base_url="https://openrouter.ai/api/v1",
                api_key=self.openrouter_api_key,
            )
            print(f"OpenRouter client initialized successfully. Using model {self.openrouter_model}")
        except Exception as e:
            print(f"Failed to initialize OpenRouter client: {e}")
            self.client = None

    def infer_with_text(self, prompt, message=None):
        """统一的文本推理接口"""
        self.messages = message if message is not None else self.messages
        self.messages.append({"role": "user", "content": prompt})

        if not self.client:
            return {'response': f"Client for platform {self.llm_platform} is not initialized.", 'messages': self.messages}

        try:
            if self.llm_platform == 'ollama':
                response_content = self.ollama_infer(self.messages)
            elif self.llm_platform in ['tongyi', 'qianfan', 'openrouter']:
                response_content = self.openai_compatible_infer(self.messages)
            elif self.llm_platform == 'spark':
                response_content = self.spark_infer(self.messages)
            else:
                response_content = f"Unsupported LLM platform: {self.llm_platform}"
        except Exception as e:
            response_content = f"Inference error on platform {self.llm_platform}: {e}"
        self.messages.append({"role": "assistant", "content": response_content})
        return {'response': response_content, 'messages': self.messages.copy()}

    def infer_with_image(self, image_path, text=None, message=None):
        """统一的图像推理接口"""
        self.messages = message if message is not None else self.messages
        prompt = text if text else self.system_text.get('text1', "Describe this image.")
        self.messages.append({"role": "user", "content": prompt})

        if not self.client:
            return {'response': f"Client for platform {self.llm_platform} is not initialized.", 'messages': self.messages}

        try:
            if self.llm_platform == 'ollama':
                response_content = self.ollama_infer(self.messages, image_path=image_path)
            elif self.llm_platform in ['tongyi', 'qianfan', 'openrouter']:
                response_content = self.openai_compatible_infer(self.messages, image_path=image_path)
            elif self.llm_platform == 'spark':
                response_content = self.spark_infer(self.messages, image_path=image_path)
            else:
                response_content = f"Image inference not supported for platform: {self.llm_platform}"
        except Exception as e:
            response_content = f"Image inference error on platform {self.llm_platform}: {e}"
        
        self.messages.append({"role": "assistant", "content": response_content})
        return {'response': response_content, 'messages': self.messages.copy()}


    def generate_image(self, prompt, width=1024, height=1024, n=1):
        """文生图功能接口，支持多平台"""
        if self.llm_platform == 'tongyi':
            return self._tongyi_generate_image(prompt, width, height, n)
        elif self.llm_platform == 'ollama':
            return self._ollama_generate_image_fallback(prompt, width, height, n)
        else:
            return {'status': 'failed', 'error': f"Image generation not supported on platform: {self.llm_platform}"}

    def _tongyi_generate_image(self, prompt, width=1024, height=1024, n=1):
        """通义千问文生图实现"""
        
        if not self.client:
            return "Tongyi client is not initialized."
        
        try:
            # 使用dashscope SDK进行文生图调用
            import dashscope
            dashscope.api_key = self.tongyi_api_key
            
            # 调用文生图API
            response = dashscope.ImageSynthesis.call(
                model=self.tongyi_media_model,
                prompt=prompt,
                n=n,
                size=f'{width}*{height}'
            )
            
            if response.status_code == 200:
                # 返回生成的图片URL
                image_urls = [item['url'] for item in response.output['results']]
                return {'image_urls': image_urls, 'status': 'success'}
            else:
                return {'error': response.message, 'status': 'failed'}
        except Exception as e:
            return {'error': str(e), 'status': 'failed'}
    
    def _ollama_generate_image_fallback(self, prompt, width=1024, height=1024, n=1):
        """Ollama平台的文生图回退方案"""
        # 由于Ollama不支持文生图，提供回退信息
        return {
            'status': 'failed',
            'error': 'Ollama不支持文生图功能。建议：\n1. 切换到tongyi平台使用通义万相\n2. 使用外部Stable Diffusion服务\n3. 使用OpenRouter的图像生成模型',
            'suggestions': [
                '切换到tongyi平台：修改配置文件中的llm_platform为tongyi',
                '使用外部服务：集成Stable Diffusion WebUI API',
                '使用OpenRouter：配置openrouter_media_model'
            ]
        }

    def text_to_image(self, prompt, width=1024, height=1024, n=1):
        """独立的文生图接口，专门用于生成图像"""
        # 直接调用generate_image方法
        return self.generate_image(prompt, width, height, n)

    def infer_with_video(self, video_path, text=None, message=None):
        """统一的视频推理接口"""
        self.messages = message if message is not None else self.messages
        prompt = text if text else "详细描述这个视频的内容。"
        self.messages.append({"role": "user", "content": prompt})

        if not self.client:
            return {'response': f"Client for platform {self.llm_platform} is not initialized.", 'messages': self.messages}

        try:
            if self.llm_platform in ['tongyi', 'qianfan', 'openrouter']:
                response_content = self.openai_compatible_infer(self.messages, video_path=video_path)
            elif self.llm_platform == 'ollama':
                response_content = self.ollama_infer(self.messages, video_path=video_path)
            else:
                response_content = f"Video inference not supported for platform: {self.llm_platform}"
        except Exception as e:
            response_content = f"Video inference error on platform {self.llm_platform}: {e}"

        self.messages.append({"role": "assistant", "content": response_content})
        return {'response': response_content, 'messages': self.messages.copy()}

    def ollama_infer(self, messages, image_path=None, video_path=None):
        """使用Ollama推理，支持工具调用和视频分析"""
        if not self.client:
            return "Error: Ollama client not initialized"

        if image_path:
            image_data = self.encode_file_to_base64(image_path)
            messages[-1]['images'] = [image_data]
        elif video_path:
            # 对于视频，提取关键帧进行分析
            print(f"开始提取视频帧: {video_path}")
            frame_images = self._extract_video_frames(video_path)
            if frame_images:
                print(f"成功提取 {len(frame_images)} 个视频帧")
                messages[-1]['images'] = frame_images
            else:
                print("视频帧提取失败")
                return "Error: Failed to extract frames from video"

        # 检查是否需要工具调用支持
        # 如果是视频或图像分析，使用普通模式获取自然语言描述
        if image_path or video_path:
            try:
                response = self.client.chat(model=self.ollama_model, messages=messages)
                return response['message']['content']
            except Exception as e:
                return f"Ollama多媒体分析失败: {e}"
        else:
            # 文本对话时尝试使用工具调用功能
            try:
                response = self.client.chat(
                    model=self.ollama_model,
                    messages=messages,
                    format='json'  # 要求JSON格式输出以便解析工具调用
                )
                return response['message']['content']
            except Exception as e:
                print(f"Ollama工具调用失败，回退到普通模式: {e}")
                # 回退到普通模式
                try:
                    response = self.client.chat(model=self.ollama_model, messages=messages)
                    return response['message']['content']
                except Exception as e2:
                    return f"Ollama推理失败: {e2}"

    def spark_infer(self, messages, image_path=None):
        """使用讯飞星火推理"""
        spark_messages = [ChatMessage(role=msg["role"], content=msg["content"]) for msg in messages]
        
        if image_path:
            image_data = self.encode_file_to_base64(image_path)
            last_user_prompt = spark_messages[-1].content
            new_content = [
                {"type": "image", "content": image_data},
                {"type": "text", "content": last_user_prompt}
            ]
            spark_messages[-1].content = new_content
        
        response = self.client.generate([spark_messages])
        if response and response.generations and len(response.generations) > 0 and len(response.generations) > 0:
            return response.generations[0][0].text
        return "Error: Received empty or malformed response from Spark."

    def openai_compatible_infer(self, messages, image_path=None, video_path=None):
        """处理所有OpenAI兼容平台的多媒体推理"""
        last_user_message = messages[-1]['content']
        if isinstance(last_user_message, list):
            last_user_prompt = " ".join([item['text'] for item in last_user_message if item['type'] == 'text'])
        else:
            last_user_prompt = last_user_message

        new_content = []

        if image_path:
            base64_media = self.encode_file_to_base64(image_path)
            new_content.append({"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{base64_media}"}})
        elif video_path:
            base64_media = self.encode_file_to_base64(video_path)
            new_content.append({"type": "video_url", "video_url": {"url": f"data:video/mp4;base64,{base64_media}"}})
        
        new_content.append({"type": "text", "text": last_user_prompt})
        
        # 创建一个新的消息列表用于本次请求，以避免修改原始列表
        request_messages = messages[:-1] + [{"role": "user", "content": new_content}]
        
        model_map = {
            'tongyi': self.tongyi_model,
            'qianfan': self.qianfan_model,
            'openrouter': self.openrouter_model
        }
        model_to_use = model_map.get(self.llm_platform, self.tongyi_model) # 默认为通义模型

        completion = self.client.chat.completions.create(model=model_to_use, messages=request_messages)
        return completion.choices[0].message.content

    # --- 现有辅助函数 ---
    def init_oline_asr(self,language):
        self.language=language
        return self.oline_asr_model
        
    def oline_asr(self,input_file):
        if self.oline_asr_model in ['paraformer-realtime-v2','paraformer-realtime-v1','paraformer-realtime-8k-v2','paraformer-realtime-8k-v1']:
            output=self.paraformer_asr_inferce(input_file)
            return output
        elif self.oline_asr_model in ['gummy-realtime-v1','gummy-chat-v1']:
            output=self.gummy_asr_inferce(input_file)
            return output

    def paraformer_asr_inferce(self,input_file):
        recognition = Recognition(model=self.oline_asr_model,
                            format='wav',
                            sample_rate=self.oline_asr_sample_rate,
                            callback=None)
        result = recognition.call(input_file)
        if result.status_code == HTTPStatus.OK:
            sentences = result.get_sentence()
            if sentences and isinstance(sentences, list):
                return ['ok', sentences.get('text', '')]
            else:
                return ['error', 'ASR Error: 大模型返回空结果，请检查账户余额或参数配置']
        else: 
            return ['error', 'ASR Error:'+result.message] 

    def gummy_asr_inferce(self,input_file):
        translator = TranslationRecognizerRealtime(
            model=self.oline_asr_model,
            format="wav",
            sample_rate=self.oline_asr_sample_rate,
            translation_target_languages=[self.language],
            translation_enabled=True,
            callback=None,
        )
        result = translator.call(input_file)
        if not result.error_message:
            output=''
            for transcription_result in result.transcription_result_list:
                output+=transcription_result.text
            return ['ok', output]
        else:
            return ['error', result.error_message]

    def init_local_asr_model(self):
        self.model_senceVoice = AutoModel(model=self.local_asr_model, trust_remote_code=False,disable_update=True)

    def tts_model_init(self,model_type='oline',language='zh'):
        if model_type=='oline':
            if self.tts_supplier=='baidu':
                self.token=self.fetch_token()
    
            self.model_type='oline'      
        elif model_type=='local':
            self.model_type='local'
            if language=='zh':
                tts_model=self.zh_tts_model
                tts_json=self.zh_tts_json
            elif language=='en':
                tts_model=self.en_tts_model
                tts_json=self.en_tts_json
            self.synthesizer = piper.PiperVoice.load(tts_model, config_path=tts_json, use_cuda=False)      

    def SenseVoiceSmall_ASR(self, input_file,language='zn'):
        res = self.model_senceVoice.generate(
            input=input_file,
            cache={},
            language=language,
            use_itn=False,
        )
        prompt = res[0]['text'].split(">")[-1]
        return ['ok', prompt]

    def voice_synthesis(self,text,path):
        if self.model_type=='oline':
            if self.tts_supplier=='baidu':
                TTS_URL = 'http://tsn.baidu.com/text2audio'
                tex = quote_plus(text)  
                params = {'tok': self.token, 'tex': tex, 'per': self.PER, 'spd': self.SPD, 'pit': self.PIT, 'vol': self.VOL, 'aue': 3, 'cuid': self.CUID,
                            'lan': 'zh', 'ctp': 1}

                data = urlencode(params)
                req = Request(TTS_URL, data.encode('utf-8'))
                try:
                    f = urlopen(req)
                    result_str = f.read()
                except  URLError as err:
                    print('asr http response http code : ' + str(err.code))
                    result_str = err.read()
                    return  1
                with open(path, 'wb') as of:
                    of.write(result_str)
                    return 0
            
            elif self.tts_supplier=='aliyun':
                self.synthesizer = SpeechSynthesizer(model= self.oline_tts_model, voice=self.voice_tone,volume=100)
                audio = self.synthesizer.call(text)
                if audio is None:
                    return 1
                else:
                    with open(path, 'wb') as f:
                        f.write(audio)  
                    return 0                                 
        elif self.model_type=='local':
            with wave.open(path, 'wb') as wav_file:
                wav_file.setnchannels(1)
                wav_file.setsampwidth(2)
                wav_file.setframerate(self.synthesizer.config.sample_rate)
                self.synthesizer.synthesize(text, wav_file)           

    def fetch_token(self):
        TOKEN_URL = 'http://aip.baidubce.com/oauth/2.0/token'
        SCOPE = 'audio_tts_post'
        params = {'grant_type': 'client_credentials',
                'client_id': self.baidu_API_KEY,
                'client_secret': self.baidu_SECRET_KEY}
        post_data = urlencode(params)
        post_data = post_data.encode('utf-8')
        req = Request(TOKEN_URL, post_data)
        try:
            f = urlopen(req, timeout=5)
            result_str = f.read()
        except URLError as err:
            print('token http response http code : ' + str(err.code))
            result_str = err.read()
        result_str = result_str.decode()
        result = json.loads(result_str)
        if ('access_token' in result.keys() and 'scope' in result.keys()):
            return result['access_token']

    def _extract_video_frames(self, video_path, max_frames=5):
        """从视频中提取关键帧用于分析"""
        try:
            import cv2
            import tempfile

            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                print(f"Error: Cannot open video file {video_path}")
                return None

            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            if total_frames == 0:
                print(f"Error: Video file {video_path} has no frames")
                cap.release()
                return None

            # 计算要提取的帧的间隔
            frame_interval = max(1, total_frames // max_frames)
            frame_images = []

            frame_count = 0
            extracted_count = 0

            while extracted_count < max_frames:
                ret, frame = cap.read()
                if not ret:
                    break

                # 每隔frame_interval帧提取一帧
                if frame_count % frame_interval == 0:
                    # 保存帧到临时文件
                    with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as temp_file:
                        temp_path = temp_file.name
                        cv2.imwrite(temp_path, frame)

                        # 编码为base64
                        frame_base64 = self.encode_file_to_base64(temp_path)
                        frame_images.append(frame_base64)

                        # 清理临时文件
                        os.unlink(temp_path)

                        extracted_count += 1

                frame_count += 1

            cap.release()
            print(f"Successfully extracted {len(frame_images)} frames from video")
            return frame_images

        except Exception as e:
            print(f"Error extracting frames from video {video_path}: {e}")
            return None

    @staticmethod
    def encode_file_to_base64(file_path):
        with open(file_path, "rb") as file:
            return base64.b64encode(file.read()).decode("utf-8")
    
    