#!/usr/bin/env python3
"""
MCP (Model Context Protocol) 服务器实现
符合MCP标准的工具服务器
"""

import json
import uuid
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum

# 导入工具链管理器
try:
    from utils.tool_chain_manager import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ana<PERSON>, ToolInterface
    TOOL_CHAIN_AVAILABLE = True
except ImportError:
    TOOL_CHAIN_AVAILABLE = False


class MCPErrorCode(Enum):
    """MCP标准错误码"""
    PARSE_ERROR = -32700
    INVALID_REQUEST = -32600
    METHOD_NOT_FOUND = -32601
    INVALID_PARAMS = -32602
    INTERNAL_ERROR = -32603
    
    # MCP特定错误码
    TOOL_NOT_FOUND = -32000
    TOOL_EXECUTION_ERROR = -32001
    RESOURCE_NOT_FOUND = -32002
    PERMISSION_DENIED = -32003


@dataclass
class MCPRequest:
    """MCP请求格式"""
    jsonrpc: str = "2.0"
    method: str = ""
    params: Dict[str, Any] = None
    id: Optional[str] = None


@dataclass
class MCPResponse:
    """MCP响应格式"""
    jsonrpc: str = "2.0"
    result: Optional[Dict[str, Any]] = None
    error: Optional[Dict[str, Any]] = None
    id: Optional[str] = None


@dataclass
class MCPTool:
    """MCP工具定义"""
    name: str
    description: str
    input_schema: Dict[str, Any]


class MCPServer:
    """
    MCP服务器实现
    管理工具注册、发现和调用
    """
    
    def __init__(self, node, tools_manager):
        self.node = node
        self.tools_manager = tools_manager
        self.tools_registry = {}
        self.server_info = {
            "name": "YahBoom Robot MCP Server",
            "version": "1.0.0",
            "protocol_version": "2024-11-05"
        }

        # 🔧 优先使用工具链管理器，回退到传统注册
        if TOOL_CHAIN_AVAILABLE and hasattr(tools_manager, 'tool_chain_manager'):
            self.tool_chain_manager = tools_manager.tool_chain_manager
            self._register_tools_from_chain_manager()
            self.node.get_logger().info("MCP Server initialized with Tool Chain Manager")
        else:
            # 回退到传统的工具注册方式
            self._register_tools_fallback()
            self.node.get_logger().info("MCP Server initialized with fallback tool registration")
    
    def _register_tools_from_chain_manager(self):
        """🔧 从工具链管理器注册工具（新方式）"""
        if not hasattr(self, 'tool_chain_manager'):
            self.node.get_logger().error("Tool Chain Manager not available")
            return

        # 从工具链管理器获取已注册的工具
        for tool_name, tool_interface in self.tool_chain_manager.tools.items():
            # 将工具链管理器的工具转换为MCP工具格式
            mcp_tool = MCPTool(
                name=tool_name,
                description=self._get_tool_description(tool_interface),
                input_schema=tool_interface.input_schema
            )
            self.tools_registry[tool_name] = mcp_tool

        self.node.get_logger().info(f"Registered {len(self.tools_registry)} tools from Tool Chain Manager")

    def _get_tool_description(self, tool_interface):
        """获取工具描述"""
        descriptions = {
            "seewhat": "Capture camera image and perform AI analysis of the current environment",
            "analyze_video": "Analyze video file content from specified path and provide detailed description",
            "generate_image": "Generate image based on text description using AI image generation",
            "visual_positioning": "Locate and identify coordinates of specified objects in an image",
            "scan_table": "Scan and extract table content from image, convert to Markdown format",
            "write_document": "Create and save document with specified content and format",
            "agent_call": "Execute complex AI Agent tasks with intelligent planning and multi-step coordination"
        }
        return descriptions.get(tool_interface.tool_name, f"Execute {tool_interface.tool_name} tool")

    def _register_tools_fallback(self):
        """🔧 回退方案：基本工具注册（当 Tool Chain Manager 不可用时）"""

        # 基本工具定义（简化版本，避免重复维护）
        basic_tools = {
            "seewhat": {
                "description": "Capture camera image and perform AI analysis of the current environment",
                "input_schema": {"type": "object", "properties": {}, "required": []}
            },
            "analyze_video": {
                "description": "Analyze video file content from specified path and provide detailed description",
                "input_schema": {
                    "type": "object",
                    "properties": {"video_path": {"type": "string", "description": "Full path to the video file to be analyzed"}},
                    "required": ["video_path"]
                }
            },
            "generate_image": {
                "description": "Generate image based on text description using AI image generation",
                "input_schema": {
                    "type": "object",
                    "properties": {"prompt": {"type": "string", "description": "Text description for image generation"}},
                    "required": ["prompt"]
                }
            }
        }

        # 注册基本工具
        for tool_name, tool_config in basic_tools.items():
            self.tools_registry[tool_name] = MCPTool(
                name=tool_name,
                description=tool_config["description"],
                input_schema=tool_config["input_schema"]
            )

        # 添加 write_document 到基本工具
        self.tools_registry["write_document"] = MCPTool(
            name="write_document",
            description="Generate and save document in various formats with specified content",
            input_schema={
                "type": "object",
                "properties": {
                    "content": {"type": "string", "description": "Main content of the document"},
                    "format": {"type": "string", "description": "Document format: md, txt, html, json", "enum": ["md", "txt", "html", "json"]},
                    "title": {"type": "string", "description": "Title of the document"}
                },
                "required": ["format", "title"]
            }
        )

        self.node.get_logger().info(f"Registered {len(self.tools_registry)} tools")
    
    def handle_request(self, request_data: str) -> str:
        """
        处理MCP请求
        """
        try:
            # 解析JSON-RPC请求
            request_json = json.loads(request_data)
            request = MCPRequest(
                jsonrpc=request_json.get("jsonrpc", "2.0"),
                method=request_json.get("method", ""),
                params=request_json.get("params", {}),
                id=request_json.get("id")
            )
            
            # 路由到相应的处理方法
            if request.method == "initialize":
                response = self._handle_initialize(request)
            elif request.method == "tools/list":
                response = self._handle_tools_list(request)
            elif request.method == "tools/call":
                response = self._handle_tools_call(request)
            elif request.method == "resources/list":
                response = self._handle_resources_list(request)
            else:
                response = self._create_error_response(
                    request.id, 
                    MCPErrorCode.METHOD_NOT_FOUND, 
                    f"Method '{request.method}' not found"
                )
            
            return json.dumps(response.__dict__, ensure_ascii=False, indent=2)
            
        except json.JSONDecodeError:
            error_response = self._create_error_response(
                None, 
                MCPErrorCode.PARSE_ERROR, 
                "Invalid JSON"
            )
            return json.dumps(error_response.__dict__, ensure_ascii=False, indent=2)
        except Exception as e:
            error_response = self._create_error_response(
                None, 
                MCPErrorCode.INTERNAL_ERROR, 
                str(e)
            )
            return json.dumps(error_response.__dict__, ensure_ascii=False, indent=2)
    
    def _handle_initialize(self, request: MCPRequest) -> MCPResponse:
        """处理初始化请求"""
        return MCPResponse(
            id=request.id,
            result={
                "protocolVersion": "2024-11-05",
                "capabilities": {
                    "tools": {},
                    "resources": {}
                },
                "serverInfo": self.server_info
            }
        )
    
    def _handle_tools_list(self, request: MCPRequest) -> MCPResponse:
        """处理工具列表请求"""
        tools_list = []
        for tool_name, tool in self.tools_registry.items():
            tools_list.append({
                "name": tool.name,
                "description": tool.description,
                "inputSchema": tool.input_schema
            })
        
        return MCPResponse(
            id=request.id,
            result={"tools": tools_list}
        )
    
    def _handle_tools_call(self, request: MCPRequest) -> MCPResponse:
        """处理工具调用请求"""
        try:
            params = request.params or {}
            tool_name = params.get("name")
            arguments = params.get("arguments", {})
            
            if tool_name not in self.tools_registry:
                return self._create_error_response(
                    request.id,
                    MCPErrorCode.TOOL_NOT_FOUND,
                    f"Tool '{tool_name}' not found"
                )
            
            # 🔧 优先使用工具链管理器执行工具
            tool_call = {
                "name": tool_name,
                "arguments": arguments
            }

            if hasattr(self, 'tool_chain_manager'):
                # 使用新的工具链管理器执行（支持数据传递）
                outputs = self.tool_chain_manager.execute_tool_chain([tool_call])
                if outputs:
                    output = outputs[0]
                    if output.success:
                        result = output.data
                        self.node.get_logger().info(f"Tool {tool_name} executed via Tool Chain Manager: success")
                    else:
                        raise Exception(output.error_message or "Tool execution failed")
                else:
                    raise Exception("No output from tool chain manager")
            else:
                # 回退到传统的工具执行方式
                result = self.tools_manager.execute_tool(tool_call)
                self.node.get_logger().info(f"Tool {tool_name} executed via legacy method")
            
            return MCPResponse(
                id=request.id,
                result={
                    "content": [
                        {
                            "type": "text",
                            "text": str(result) if result else "Tool executed successfully"
                        }
                    ]
                }
            )
            
        except Exception as e:
            return self._create_error_response(
                request.id,
                MCPErrorCode.TOOL_EXECUTION_ERROR,
                str(e)
            )
    
    def _handle_resources_list(self, request: MCPRequest) -> MCPResponse:
        """处理资源列表请求"""
        # 这里可以列出可访问的资源（文件、数据库等）
        return MCPResponse(
            id=request.id,
            result={"resources": []}
        )
    
    def _create_error_response(self, request_id: Optional[str], error_code: MCPErrorCode, message: str) -> MCPResponse:
        """创建错误响应"""
        return MCPResponse(
            id=request_id,
            error={
                "code": error_code.value,
                "message": message
            }
        )
    
    def get_tools_for_prompt(self) -> str:
        """
        为LLM生成工具描述
        这替代了prompt.py中的硬编码工具列表
        """
        tools_description = "可用工具 (Available Tools):\n"
        
        for tool_name, tool in self.tools_registry.items():
            # 从schema生成参数描述
            params_desc = ""
            if tool.input_schema.get("properties"):
                params = []
                for param_name, param_info in tool.input_schema["properties"].items():
                    params.append(f"{param_name}: {param_info.get('description', param_info.get('type', 'string'))}")
                if params:
                    params_desc = f"({', '.join(params)})"
            
            tools_description += f"- {tool_name}{params_desc}: {tool.description}\n"
        
        return tools_description
