import os
import cv2
import json
import rclpy
from rclpy.node import Node
from cv_bridge import CvBridge
from sensor_msgs.msg import Image
from std_msgs.msg import String,Bool
from utils import large_model_interface
from utils.tools_manager import ToolsManager # 导入新的ToolsManager
from ament_index_python.packages import get_package_share_directory
import threading
import pygame
import numpy as np
import atexit
from interfaces.srv import TextToImage

class LargeModelService(Node):
    def __init__(self):
        super().__init__('LargeModelService')

        self.init_param_config()#初始化参数配置
        self.init_largemodel()#初始化大模型
        self.init_ros_comunication()#初始化ros通信
        self.init_camera()#初始化相机
    
        # 添加摄像头相关属性初始化
        self.camera_initialized = False
        self.cap = None
        #bridge用于处理图片
        self.bridge = CvBridge()

        #初始化音频播放器
        pygame.mixer.init()
        self.current_thread = None
        self.stop_event = threading.Event()

        #用于在多轮对话中保存和追溯Ollama的message
        self.message_file_path = os.path.join(self.pkg_path, "resources_file", "conversation_message.json")
        self.init_message_file()
        atexit.register(self.cleanup_message_file)

        # 初始化工具管理器
        self.tools_manager = ToolsManager(self)

        # ✅ 初始化AI Agent
        from utils.ai_agent import AIAgent
        self.ai_agent = AIAgent(self, self.tools_manager)

        # ✅ 初始化MCP服务器
        from utils.mcp_server import MCPServer
        self.mcp_server = MCPServer(self, self.tools_manager)

        # ✅ 初始化MCP客户端（用于连接远程服务器）
        from utils.mcp_client import MCPClient
        self.mcp_client = MCPClient(self)

        # ✅ 加载远程MCP服务器配置
        self.load_remote_mcp_servers()

        # ✅ 更新模型以使用MCP动态工具发现
        self.update_model_with_mcp()

        #打印日志
        self.get_logger().info('LargeModelService node Initialization completed...')

    def init_param_config(self):
        src_path = os.path.expanduser("~/yahboom_ws/src/largemodel")
        if os.path.exists(src_path):
            self.pkg_path = src_path
            self.get_logger().info(f"Using development path: {self.pkg_path}")
        else:
            self.pkg_path = get_package_share_directory('largemodel')
            self.get_logger().info(f"Using install path: {self.pkg_path}")
        #参数声明
        self.declare_parameter('language', 'zh')
        self.declare_parameter('text_chat_mode', False)
        self.declare_parameter('llm_platform', 'ollama') # 新增llm_platform参数
        self.declare_parameter('useolinetts', False)
        #获取参数服务器参数
        self.language = self.get_parameter('language').get_parameter_value().string_value 
        self.text_chat_mode = self.get_parameter('text_chat_mode').get_parameter_value().bool_value 
        self.useolinetts = self.get_parameter('useolinetts').get_parameter_value().bool_value
        self.llm_platform = self.get_parameter('llm_platform').get_parameter_value().string_value

        #设置TTS输出路径
        if self.useolinetts:
            self.tts_out_path = os.path.join(self.pkg_path, "resources_file", "tts_output.mp3")
        else:
            self.tts_out_path = os.path.join(self.pkg_path, "resources_file", "tts_output.wav")

    def init_largemodel(self):
        #创建模型接口客户端,并传入平台名称
        self.model_client=large_model_interface.model_interface(llm_platform=self.llm_platform)
        #调用LLM初始化
        self.model_client.init_llm()
        #初始化大模型接口的语言
        self.model_client.init_language(self.language)
        self.get_logger().info(f'Using LLM platform: {self.llm_platform}')

    def load_remote_mcp_servers(self):
        """加载远程MCP服务器配置"""
        try:
            import yaml
            config_file = os.path.join(self.pkg_path, "config", "remote_mcp_servers.yaml")

            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    servers_config = yaml.safe_load(f)

                for server_name, config in servers_config.items():
                    if isinstance(config, dict) and config.get('enabled', False):
                        self.get_logger().info(f"Connecting to remote MCP server: {server_name}")
                        success = self.mcp_client.add_remote_server(server_name, config)
                        if success:
                            self.get_logger().info(f"Successfully connected to {server_name}")
                        else:
                            self.get_logger().warn(f"Failed to connect to {server_name}")
            else:
                self.get_logger().info("No remote MCP servers configuration found")

        except Exception as e:
            self.get_logger().error(f"Error loading remote MCP servers: {e}")

    def update_model_with_mcp(self):
        """在MCP服务器初始化后更新模型的prompt"""
        if hasattr(self, 'mcp_server'):
            # 传递mcp_server和mcp_client给model_client
            self.model_client.init_messages(self.mcp_server, self.mcp_client)
            self.get_logger().info('Model updated with MCP server and client')

    def init_ros_comunication(self):
        # asr话题订阅者
        self.asrsub = self.create_subscription(String,'asr', self.asr_callback,1)
        #创建文字交互发布者
        self.text_pub = self.create_publisher(String, "text_response", 1)
        #创建TTS发布者
        self.TTS_publisher = self.create_publisher(String, "tts_topic", 5)
        #初始化TTS系统
        self.system_sound_init()
        #创建唤醒订阅者
        self.wakeup_sub = self.create_subscription(Bool, 'wakeup', self.wakeup_callback, 5)
        # 创建文生图服务
        self.text_to_image_service = self.create_service(TextToImage, 'text_to_image', self.text_to_image_callback)

    def init_message_file(self):
        """初始化message文件"""
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(self.message_file_path), exist_ok=True)
            
            # 如果文件不存在或为空，创建空的message数组
            if not os.path.exists(self.message_file_path):
                with open(self.message_file_path, 'w', encoding='utf-8') as f:
                    json.dump([], f, ensure_ascii=False, indent=2)  # 确保是空数组
            else:
                # 检查现有文件格式
                try:
                    with open(self.message_file_path, 'r', encoding='utf-8') as f:
                        content = f.read().strip()
                        if content:
                            json.loads(content)  # 验证JSON格式
                        else:
                            # 文件为空，写入空数组
                            with open(self.message_file_path, 'w', encoding='utf-8') as f:
                                json.dump([], f, ensure_ascii=False, indent=2)
                except json.JSONDecodeError:
                    # 文件格式错误，重新初始化
                    self.get_logger().warning("Message file format error, reinitializing...")
                    with open(self.message_file_path, 'w', encoding='utf-8') as f:
                        json.dump([], f, ensure_ascii=False, indent=2)
                    
            self.get_logger().info(f"Message file initialized at: {self.message_file_path}")
        except Exception as e:
            self.get_logger().error(f"Failed to initialize message file: {e}")

    def save_message_to_file(self, messages):
        """保存message到文件"""
        try:
            with open(self.message_file_path, 'w', encoding='utf-8') as f:
                messages_to_save = messages if messages is not None else []
                
                # 确保 system prompt 的唯一性和存在性
                # 1. 从内存中获取包含 system prompt 的、正确的初始消息列表
                initial_template = self.model_client.messages.copy()
                
                # 2. 从待保存的消息中，只筛选出 user 和 assistant 的对话历史
                history_only = [m for m in messages_to_save if m.get('role') in ['user', 'assistant']]
                
                # 3. 将正确的初始模板和纯净的对话历史合并
                final_messages = initial_template + history_only

                # 限制messages大小，防止文件过大
                if len(final_messages) > 50:
                    # 只截断 user 和 assistant 的消息
                    recent_history = [m for m in final_messages if m.get('role') in ['user', 'assistant']][-40:]
                    # 重新用初始模板和截断后的历史合并
                    final_messages = initial_template + recent_history
                    self.get_logger().info("Messages truncated before saving to file")
                
                json.dump(final_messages, f, ensure_ascii=False, indent=2)
                    
            self.get_logger().debug(f"Messages saved to file, size: {len(final_messages)}")
        except Exception as e:
            self.get_logger().error(f"Failed to save messages to file: {e}")



    def load_message_from_file(self):
        """从文件加载messages"""
        try:
            if os.path.exists(self.message_file_path):
                with open(self.message_file_path, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                    if not content:
                        return []  # Return empty list for empty file
                    messages = json.loads(content)
                    
                # 检查并限制消息数量
                if isinstance(messages, list) and len(messages) > 100:
                    system_msgs = [msg for msg in messages if msg.get('role') == 'system']
                    recent_msgs = [msg for msg in messages if msg.get('role') != 'system'][-80:]
                    messages = system_msgs + recent_msgs
                    self.save_message_to_file(messages)  # 保存截断后的messages
                    self.get_logger().info("Messages truncated after loading from file")
                    
                self.get_logger().debug(f"Messages loaded from file, count: {len(messages) if messages else 0}")
                return messages
            else:
                return []  # File doesn't exist, return empty list
        except Exception as e:
            self.get_logger().error(f"Failed to load messages from file: {e}")
            self.clear_message_file()  # Clear corrupted file
            return []  # Return empty list

    def cleanup_message_file(self):
        """清理message文件"""
        try:
            if os.path.exists(self.message_file_path):
                os.remove(self.message_file_path)
                print(f"Message file cleaned up: {self.message_file_path}")
        except Exception as e:
            print(f"Failed to cleanup message file: {e}")

    def clear_message_file(self):
        """清空message内容"""
        self.save_message_to_file(None)
        self.get_logger().info("Message file cleared")



    def init_camera(self):
        """初始化摄像头"""
        # 摄像头配置从yaml文件读取
        self.declare_parameter('auto_camera', True)
        self.declare_parameter('camera_width', 640)
        self.declare_parameter('camera_height', 480)
        
        self.auto_camera = self.get_parameter('auto_camera').get_parameter_value().bool_value
        self.camera_width = self.get_parameter('camera_width').get_parameter_value().integer_value
        self.camera_height = self.get_parameter('camera_height').get_parameter_value().integer_value
        
        # 初始化摄像头变量
        self.cap = None
        self.camera_initialized = False
        self.last_frame = None
        
        if self.auto_camera:
            # 创建定时器，定期检查摄像头状态
            self.camera_check_timer = self.create_timer(1.0, self.camera_check_callback)
        
        self.get_logger().info('Camera system initialized')

    def camera_check_callback(self):
        """定期检查摄像头状态"""
        if not self.camera_initialized:
            self.init_camera_device()

    def init_camera_device(self):
        """初始化摄像头设备"""
        try:
            # 尝试USB摄像头
            cap = cv2.VideoCapture(0)
            if cap.isOpened():
                # ✅ 设置摄像头参数，减少缓存问题
                cap.set(cv2.CAP_PROP_FRAME_WIDTH, self.camera_width)
                cap.set(cv2.CAP_PROP_FRAME_HEIGHT, self.camera_height)
                cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)  # 设置缓冲区大小为1，减少延迟
                cap.set(cv2.CAP_PROP_FPS, 30)  # 设置帧率

                # ✅ 预热摄像头，清空初始缓冲区
                for _ in range(10):
                    ret, _ = cap.read()
                    if not ret:
                        break

                self.cap = cap
                self.get_logger().info(f"Camera initialized successfully at {self.camera_width}x{self.camera_height} with buffer optimization")
                self.camera_initialized = True
                return True
            else:
                self.get_logger().error("Failed to open camera")
                cap.release()
                self.cap = None
                self.camera_initialized = False
                return False
        except Exception as e:
            self.get_logger().error(f"Camera initialization failed: {e}")
            self.cap = None
            self.camera_initialized = False
            return False


    # TTS初始化
    def system_sound_init(self):
        """Initialize TTS system"""
        model_type = "oline" if self.useolinetts else "local"
        self.model_client.tts_model_init(model_type, self.language)
        self.get_logger().info(f'TTS initialized with {model_type} model')

    # 唤醒回调
    def wakeup_callback(self, msg):
        """Handle wake-up signals"""
        if msg.data:
            if pygame.mixer.music.get_busy():      
                self.stop_event.set()
            self.get_logger().info('Received wake-up signal')

    # def interrupt_flag_callback(self,msg):
    #     if msg.data :
    #         self.new_order_cycle=True      
    def asr_callback(self,msg):
        """处理ASR消息的回调函数"""
        # 1. 从文件加载历史记录
        messages_to_use = self.load_message_from_file()

        # 2. 检查历史记录是否有效。如果为空或不包含system prompt，
        #    则说明是一次全新的对话，使用内存中的模板进行初始化。
        if not messages_to_use or not any(d.get('role') == 'system' for d in messages_to_use):
            self.get_logger().info("No valid history in file, starting a new conversation with template.")
            messages_to_use = self.model_client.messages.copy()

        # 调用新的统一接口。infer_with_text会负责将当前用户输入(msg.data)追加进去
        result = self.model_client.infer_with_text(msg.data, message=messages_to_use)

        self.process_model_result(result)

    def process_model_result(self, result, from_seewhat=False):
        """处理模型返回的结果"""
        response_text = ""
        tools_list = []

        if isinstance(result, dict):
            new_messages = result.get('messages')
            if new_messages:
                self.save_message_to_file(new_messages)
            response_text = result.get('response', '')
        else:
            self.clear_message_file()
            response_text = result

        # ✅ 提取工具调用和用户友好的回复
        user_friendly_response = response_text
        try:
            json_str = self.extract_json_content(response_text)
            response_json = json.loads(json_str)

            # 提取工具列表
            tools_list = response_json.get("tools", [])

            # ✅ 提取用户友好的回复文本，而不是技术性的JSON
            extracted_response = response_json.get("response", "")
            if extracted_response and not extracted_response.startswith('{'):
                # 如果response字段包含用户友好的文本，使用它
                user_friendly_response = extracted_response
            else:
                # 否则生成一个友好的回复
                if tools_list:
                    tool_names = [tool.get('name', 'unknown') if isinstance(tool, dict) else str(tool) for tool in tools_list]
                    user_friendly_response = f"好的，我来为您执行这些操作：{', '.join(tool_names)}"
                else:
                    user_friendly_response = "我正在处理您的请求..."

            self.get_logger().info(f'✅ JSON解析成功，提取到 {len(tools_list)} 个工具')
        except Exception as e:
            self.get_logger().warning(f'JSON parsing from response failed: {e}. Using raw response.')
            tools_list = []

        if from_seewhat:
            # 更智能的循环检测：只阻止重复的 seewhat 调用
            filtered_tools = []
            for tool in tools_list:
                if isinstance(tool, dict):
                    tool_name = tool.get("name")
                    if tool_name == "seewhat":
                        self.get_logger().info("🚫 阻止重复的 seewhat 调用以防止循环")
                        continue
                elif isinstance(tool, str) and "seewhat" in tool:
                    self.get_logger().info("🚫 阻止重复的 seewhat 调用以防止循环")
                    continue
                filtered_tools.append(tool)

            if len(filtered_tools) != len(tools_list):
                self.get_logger().info(f"✅ 过滤后保留 {len(filtered_tools)}/{len(tools_list)} 个工具")
            tools_list = filtered_tools

        if self.text_chat_mode:
            # ✅ 在文字模式下显示用户友好的回复，而不是技术性的JSON
            msg = String(data=user_friendly_response)
            self.text_pub.publish(msg)
            self.get_logger().info(f'Text mode response: {user_friendly_response}')
        else:
            # ✅ 在语音模式下也使用用户友好的回复
            if user_friendly_response:
                try:
                    self.model_client.voice_synthesis(user_friendly_response, self.tts_out_path)
                    self.play_audio_async(self.tts_out_path, True)
                    self.TTS_publisher.publish(String(data=user_friendly_response))
                except Exception as e:
                    self.get_logger().warning(f"TTS synthesis failed: {e}")

        self.get_logger().info(f'🔧 准备执行工具列表，共 {len(tools_list)} 个工具')
        self.execute_tools(tools_list)

    def execute_tools(self, tools_list):
        """执行工具列表 - 简化版本"""
        self.get_logger().info(f'🔧 execute_tools被调用，工具列表: {tools_list}')
        if not tools_list:
            self.get_logger().info('🔧 工具列表为空，跳过执行')
            return

        # 🎯 简化逻辑：多工具直接调用AI Agent，单工具查找并执行
        if len(tools_list) > 1:
            self.get_logger().info(f"检测到多工具调用({len(tools_list)}个工具) - 使用AI Agent处理")
            # 构建任务描述
            tool_names = [self._extract_tool_name(tool) for tool in tools_list]
            task_description = f"请执行以下工具：{', '.join(tool_names)}"

            # 添加工具参数信息
            tool_details = []
            for tool in tools_list:
                tool_name = self._extract_tool_name(tool)
                arguments = self._extract_tool_arguments(tool)
                if arguments:
                    tool_details.append(f"{tool_name}: {arguments}")
                else:
                    tool_details.append(tool_name)

            if tool_details:
                task_description += f"\n工具详情：{'; '.join(tool_details)}"

            # 执行AI Agent
            self._execute_agent_task(task_description)
            return

        # 单工具调用 - 查找并执行
        tool_call = tools_list[0]
        tool_name = self._extract_tool_name(tool_call)
        arguments = self._extract_tool_arguments(tool_call)

        self.get_logger().info(f"执行单工具: {tool_name}")

        # 特殊处理：agent_call直接执行
        if tool_name == "agent_call":
            task_description = arguments.get("task", "") if arguments else ""
            if task_description:
                self._execute_agent_task(task_description)
            else:
                self.get_logger().error("Missing 'task' argument for agent_call")
            return

        # 检查工具是否在工具库中
        if self._is_tool_available(tool_name):
            self._execute_single_tool_by_name(tool_name, arguments)
        else:
            self.get_logger().error(f"工具 '{tool_name}' 不在可用工具库中")
            self._provide_feedback(f"抱歉，工具 '{tool_name}' 不可用")

    def _extract_tool_name(self, tool_call):
        """提取工具名称"""
        if isinstance(tool_call, dict):
            return tool_call.get("name", "")
        elif isinstance(tool_call, str):
            # 处理字符串格式，如 "seewhat()" 或 "agent_call('task')"
            if "(" in tool_call:
                return tool_call.split("(")[0]
            return tool_call
        return ""

    def _extract_tool_arguments(self, tool_call):
        """提取工具参数"""
        if isinstance(tool_call, dict):
            return tool_call.get("arguments", {})
        elif isinstance(tool_call, str):
            # 简单的字符串参数提取（可以根据需要扩展）
            if "agent_call" in tool_call and "'" in tool_call:
                parts = tool_call.split("'")
                if len(parts) >= 2:
                    return {"task": parts[1]}
            return {}
        return {}

    def _is_tool_available(self, tool_name):
        """检查工具是否在可用工具库中"""
        # 检查tools_manager中的可用工具
        if hasattr(self.tools_manager, 'tool_chain_manager') and hasattr(self.tools_manager.tool_chain_manager, 'tools'):
            return tool_name in self.tools_manager.tool_chain_manager.tools

        # 检查MCP服务器中的工具
        if hasattr(self, 'mcp_server') and hasattr(self.mcp_server, 'tools'):
            return tool_name in self.mcp_server.tools

        # 基本工具列表
        basic_tools = ["seewhat", "generate_image", "analyze_video", "write_document", "scan_table", "visual_positioning"]
        return tool_name in basic_tools

    def _execute_single_tool_by_name(self, tool_name, arguments):
        """根据工具名称执行单个工具"""
        try:
            # 构建MCP请求
            mcp_request = {
                "jsonrpc": "2.0",
                "method": "tools/call",
                "params": {
                    "name": tool_name,
                    "arguments": arguments or {}
                },
                "id": "single_tool"
            }

            # 通过MCP服务器执行
            response = self.mcp_server.handle_request(json.dumps(mcp_request))
            self.get_logger().info(f"工具 {tool_name} 执行完成")

            # 处理响应并提供反馈
            self._handle_tool_response(tool_name, response)

        except Exception as e:
            self.get_logger().error(f"执行工具 {tool_name} 失败: {e}")
            self._provide_feedback(f"执行工具 {tool_name} 时出现错误")

    def _execute_agent_task(self, task_description):
        """执行AI Agent任务"""
        try:
            self.get_logger().info(f"执行AI Agent任务: {task_description}")
            agent_result = self.ai_agent.execute_task(task_description)

            if agent_result and agent_result.get("success"):
                feedback_message = "✅ AI Agent任务完成！"
                if agent_result.get("generated_files"):
                    feedback_message += "\n📄 生成的文件："
                    for file_path in agent_result.get("generated_files", []):
                        filename = file_path.split("/")[-1]
                        feedback_message += f"\n  - {filename}"

                feedback_message += f"\n📊 执行了 {agent_result.get('steps_executed', 0)} 个步骤"
                self._provide_feedback(feedback_message)
            else:
                self._provide_feedback("❌ AI Agent任务执行失败")

        except Exception as e:
            self.get_logger().error(f"AI Agent执行失败: {e}")
            self._provide_feedback("AI Agent执行时出现错误")

    def _handle_tool_response(self, tool_name, response):
        """处理工具响应"""
        # 这里可以根据不同工具类型处理响应
        # 目前简化处理
        if response:
            self.get_logger().info(f"工具 {tool_name} 响应: {response}")

    def _provide_feedback(self, message):
        """统一的反馈机制"""
        self.get_logger().info(f"反馈: {message}")

        if self.text_chat_mode:
            # 文字模式
            text_msg = String(data=message)
            self.text_pub.publish(text_msg)
        else:
            # 语音模式
            try:
                self.model_client.voice_synthesis(message, self.tts_out_path)
                self.play_audio_async(self.tts_out_path)
            except Exception as e:
                self.get_logger().error(f"TTS反馈失败: {e}")

    def _execute_single_tool(self, tool_call):
        """
        简化的单工具执行（LLM优先设计）

        🧠 设计理念：
        - 直接调用工具，不做任何参数修改
        - 完全信任LLM提供的参数
        """
        if isinstance(tool_call, dict) and "name" in tool_call:
            tool_name = tool_call.get("name")
            arguments = tool_call.get("arguments", {})

            self.get_logger().info(f"执行工具: {tool_name}")
            self.get_logger().info(f"LLM提供的参数: {arguments}")

            # 🎯 直接通过MCP服务器调用，不做任何增强
            mcp_request = {
                "jsonrpc": "2.0",
                "method": "tools/call",
                "params": {
                    "name": tool_name,
                    "arguments": arguments
                },
                "id": "single_tool"
            }

            response = self.mcp_server.handle_request(json.dumps(mcp_request))
            self.get_logger().info(f"工具 {tool_name} 执行完成")

        elif isinstance(tool_call, str):
            # 处理字符串格式的工具调用
            self.tools_manager.execute_tool(tool_call)

        else:
            self.get_logger().error(f"Unknown tool call format: {type(tool_call)}")

    def _get_current_time(self):
        """获取当前时间字符串"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    def _extract_clean_response(self, data):
        """
        提取干净的响应内容，用于交互模式显示
        """
        if not data:
            return "无内容"

        # 如果是字符串且看起来像JSON
        if isinstance(data, str):
            if data.startswith('{') and data.endswith('}'):
                try:
                    import json
                    parsed_data = json.loads(data)
                    if isinstance(parsed_data, dict) and 'response' in parsed_data:
                        return parsed_data['response']
                except json.JSONDecodeError:
                    pass
            # 如果包含```json标记，提取其中的response
            elif '```json' in data:
                try:
                    json_str = self.extract_json_content(data)
                    parsed_data = json.loads(json_str)
                    if isinstance(parsed_data, dict) and 'response' in parsed_data:
                        return parsed_data['response']
                except:
                    pass
            return data

        # 如果是字典
        elif isinstance(data, dict):
            return data.get('response', str(data))

        # 其他情况
        return str(data)



    def play_audio(self, file_path, feedback=False):
        """同步方式播放音频函数 Play audio file"""
        try:
            pygame.mixer.music.load(file_path)
            pygame.mixer.music.play()
            while pygame.mixer.music.get_busy():
                if self.stop_event.is_set():
                    pygame.mixer.music.stop()
                    self.stop_event.clear()
                    return
                pygame.time.delay(100)
        except Exception as e:
            self.get_logger().error(f"Error playing audio: {e}")

    def play_audio_async(self, file_path, feedback=False):
        """异步方式播放音频函数 Play audio asynchronously"""
        if self.current_thread and self.current_thread.is_alive():
            self.stop_event.set()
            self.current_thread.join()
            self.stop_event.clear()
        
        def target():
            self.play_audio(file_path, feedback)
        
        self.current_thread = threading.Thread(target=target)
        self.current_thread.daemon = True
        self.current_thread.start()

    @staticmethod
    def extract_json_content(raw_content):#解析变量提取json
        try:
            # 方法一：分割代码块
            if '```json' in raw_content:
                # 分割代码块并取中间部分
                json_str = raw_content.split('```json')[1].split('```')[0].strip()
            elif '```' in raw_content:
                # 处理没有指定类型的代码块
                json_str = raw_content.split('```')[1].strip()
            else:
                # 方法二：使用更强大的JSON提取方法
                json_str = LargeModelService._extract_json_from_text(raw_content)

            # 方法三：备用的简单正则表达式（如果上面的方法失败）
            if not json_str or not json_str.strip().startswith('{'):
                import re
                match = re.search(r'\{.*\}', raw_content, re.DOTALL)
                if match:
                    json_str = match.group()

            # 检查是否找到了可能的JSON内容
            if not json_str or not json_str.strip().startswith('{'):
                # 处理特殊字符，使文本安全用于JSON
                # 替换所有可能导致JSON解析错误的字符
                safe_content = raw_content.replace('\\', '\\\\')  # 先处理反斜杠
                safe_content = safe_content.replace('"', '\\"')    # 处理引号
                safe_content = safe_content.replace('\n', '\\n')   # 处理换行
                safe_content = safe_content.replace('\r', '\\r')   # 处理回车
                safe_content = safe_content.replace('\t', '\\t')   # 处理制表符
                safe_content = safe_content.replace('\b', '\\b')   # 处理退格
                safe_content = safe_content.replace('\f', '\\f')   # 处理换页

                return '{"response": "' + safe_content + '"}'

            return json_str

        except Exception as e:
            # 同样处理特殊字符
            if raw_content:
                safe_content = str(raw_content)
                safe_content = safe_content.replace('\\', '\\\\')
                safe_content = safe_content.replace('"', '\\"')
                safe_content = safe_content.replace('\n', '\\n')
                safe_content = safe_content.replace('\r', '\\r')
                safe_content = safe_content.replace('\t', '\\t')
                safe_content = safe_content.replace('\b', '\\b')
                safe_content = safe_content.replace('\f', '\\f')

                return '{"response": "' + safe_content + '"}'
            return '{}'

    @staticmethod
    def _extract_json_from_text(text):
        """从文本中提取完整的JSON对象，支持嵌套结构"""
        # 找到第一个 { 的位置
        start_pos = text.find('{')
        if start_pos == -1:
            return ""

        # 从第一个 { 开始，找到匹配的 }
        brace_count = 0
        in_string = False
        escape_next = False

        for i, char in enumerate(text[start_pos:], start_pos):
            if escape_next:
                escape_next = False
                continue

            if char == '\\':
                escape_next = True
                continue

            if char == '"' and not escape_next:
                in_string = not in_string
                continue

            if not in_string:
                if char == '{':
                    brace_count += 1
                elif char == '}':
                    brace_count -= 1
                    if brace_count == 0:
                        # 找到了完整的JSON对象
                        return text[start_pos:i+1]

        # 如果没有找到完整的JSON，返回空字符串
        return ""

    def text_to_image_callback(self, request, response):
        """处理文生图请求的回调函数"""
        try:
            # 调用模型客户端的文生图功能
            result = self.model_client.generate_image(
                prompt=request.prompt,
                width=request.width,
                height=request.height,
                n=request.num_images
            )
            
            if isinstance(result, dict) and result.get('status') == 'success':
                response.success = True
                response.image_urls = result['image_urls']
                response.message = "Image generation successful"
            else:
                response.success = False
                response.message = result.get('error', 'Unknown error occurred') if isinstance(result, dict) else str(result)
        except Exception as e:
            self.get_logger().error(f"Text-to-image generation failed: {e}")
            response.success = False
            response.message = f"Image generation failed: {str(e)}"
        
        return response

    def cleanup_temp_image(self):
        """清理临时图片文件"""
        try:
            # 清理摄像头拍摄的临时图片文件
            temp_dir = os.path.join(self.pkg_path, "resources_file", "temp_images")
            if os.path.exists(temp_dir):
                import shutil
                shutil.rmtree(temp_dir)
                self.get_logger().info("Temporary image files cleaned up")
        except Exception as e:
            self.get_logger().warning(f"Failed to cleanup temporary image files: {e}")

def main(args=None):
    rclpy.init(args=args)
    model_service = LargeModelService()
    try:
        rclpy.spin(model_service)
    except KeyboardInterrupt:
        model_service.get_logger().info("KeyboardInterrupt received, shutting down...")
    except Exception as e:
        model_service.get_logger().error(f"Unexpected error: {e}")
    finally:
        # 清理临时文件
        model_service.cleanup_temp_image()
        model_service.cleanup_message_file()
        model_service.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
