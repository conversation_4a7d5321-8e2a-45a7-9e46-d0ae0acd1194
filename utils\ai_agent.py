import json
import time
import os
from datetime import datetime
from typing import Dict, List, Any, Optional
from ament_index_python.packages import get_package_share_directory


class AIAgent:
    """
    AI Agent核心类
    负责复杂任务的规划、分解和执行
    """
    
    def __init__(self, node, tools_manager):
        """
        初始化AI Agent
        :param node: ROS2节点实例
        :param tools_manager: 工具管理器实例
        """
        self.node = node
        self.tools_manager = tools_manager

        # Agent配置
        self.max_iterations = 10
        self.max_execution_time = 300
        self.retry_attempts = 2
        self.retry_interval = 1

        # 当前任务状态
        self.current_task = None
        self.task_steps = []

        # 简单的执行历史（只保存最近10个任务）
        self.execution_history = []
        self.max_history = 10

        self.node.get_logger().info("AI Agent initialized successfully")
    
    def execute_task(self, task_description: str) -> Dict[str, Any]:
        """
        执行AI Agent任务的主入口
        """
        self.node.get_logger().info(f"AI Agent开始执行任务: {task_description}")
        
        try:
            # 开始任务
            self.current_task = {
                "description": task_description,
                "start_time": datetime.now(),
                "status": "executing",
                "steps": []
            }

            # 执行Agent工作流
            result = self._execute_agent_workflow(task_description)

            # 完成任务并保存到历史
            self.current_task["end_time"] = datetime.now()
            self.current_task["success"] = result["success"]
            self.current_task["message"] = result["message"]

            # 保存到历史记录（只保留最近的任务）
            self.execution_history.append(self.current_task.copy())
            if len(self.execution_history) > self.max_history:
                self.execution_history.pop(0)

            # 通过TTS反馈结果
            feedback = result["message"]
            self.node.model_client.voice_synthesis(feedback, self.node.tts_out_path)
            self.node.play_audio_async(self.node.tts_out_path)

            return result

        except Exception as e:
            self.node.get_logger().error(f"AI Agent任务执行失败: {e}")
            error_result = {"success": False, "message": f"Agent执行失败: {str(e)}"}

            # 记录失败的任务
            if self.current_task:
                self.current_task["end_time"] = datetime.now()
                self.current_task["success"] = False
                self.current_task["message"] = error_result["message"]
                self.execution_history.append(self.current_task.copy())

            return error_result
    
    def _execute_agent_workflow(self, task_description: str) -> Dict[str, Any]:
        """
        执行Agent工作流：规划 -> 执行 -> 反思 -> 调整
        """
        try:
            # 第一步：任务规划
            self.node.get_logger().info("AI Agent开始任务规划阶段")
            plan_result = self._plan_task(task_description)
            self.node.get_logger().info(f"任务规划结果: {plan_result}")

            if not plan_result["success"]:
                self.node.get_logger().error(f"任务规划失败: {plan_result.get('message', '未知错误')}")
                return plan_result

            self.task_steps = plan_result["steps"]
            self.node.get_logger().info(f"任务规划完成，共{len(self.task_steps)}个步骤")

            # 第二步：执行任务步骤
            execution_results = []
            step_outputs = {}  # 存储步骤输出，用于后续步骤
            tool_outputs = []  # 存储ToolOutput格式的输出，用于工具链数据传递

            for i, step in enumerate(self.task_steps):
                self.node.get_logger().info(f"执行步骤 {i+1}/{len(self.task_steps)}: {step['description']}")

                # 跳过没有工具的步骤
                if not step.get("tool") or step.get("tool").strip() == "":
                    self.node.get_logger().warn(f"步骤 {i+1} 没有指定工具，跳过执行")
                    execution_results.append({
                        "success": True,
                        "step_id": step.get("id"),
                        "description": step["description"],
                        "message": "跳过执行（无工具）"
                    })
                    continue

                # 处理步骤参数中的占位符
                processed_step = self._process_step_parameters(step, step_outputs)

                # 记录步骤到当前任务
                step_record = {
                    "step_id": step.get("id"),
                    "description": step["description"],
                    "start_time": datetime.now()
                }

                # ✅ 传递前面工具的输出给当前步骤
                step_result = self._execute_step(processed_step, tool_outputs)
                execution_results.append(step_result)

                # 保存步骤输出供后续步骤使用（保持原有逻辑）
                if step_result.get("success"):
                    step_outputs[f"step{step.get('id')}_output"] = step_result.get("result")

                    # ✅ 转换为ToolOutput格式并添加到工具输出列表
                    from utils.tool_chain_manager import ToolOutput
                    tool_output = ToolOutput(
                        tool_name=step.get("tool"),
                        success=True,
                        data=step_result.get("result"),
                        metadata={"step_id": step.get("id"), "description": step.get("description")},
                        error_message=None
                    )
                    tool_outputs.append(tool_output)
                    self.node.get_logger().info(f"✅ 步骤 {i+1} 输出已添加到工具链传递列表")
                else:
                    # 失败的步骤也要添加到工具输出列表，但标记为失败
                    from utils.tool_chain_manager import ToolOutput
                    tool_output = ToolOutput(
                        tool_name=step.get("tool"),
                        success=False,
                        data=None,
                        metadata={"step_id": step.get("id"), "description": step.get("description")},
                        error_message=step_result.get("error", "步骤执行失败")
                    )
                    tool_outputs.append(tool_output)

                # 完善步骤记录
                step_record["end_time"] = datetime.now()
                step_record["success"] = step_result["success"]
                step_record["result"] = step_result
                self.current_task["steps"].append(step_record)

                # 如果步骤失败，记录错误但继续执行
                if not step_result.get("success", False):
                    self.node.get_logger().warn(f"步骤 {i+1} 执行失败: {step_result.get('message', '未知错误')}")
                    # 对于非关键步骤，继续执行后续步骤
                    if step.get("tool") not in ["seewhat"]:  # seewhat是关键步骤
                        self.node.get_logger().info(f"非关键步骤失败，继续执行后续步骤")
                    else:
                        self.node.get_logger().error(f"关键步骤失败，但仍尝试继续执行")
                else:
                    self.node.get_logger().info(f"步骤 {i+1} 执行成功")

            # 第三步：总结结果
            summary = self._summarize_execution(task_description, execution_results)

            # 检查是否有文档生成
            generated_files = []
            for result in execution_results:
                if result.get("success") and "已保存到:" in str(result.get("result", "")):
                    # 提取文件路径
                    result_text = str(result.get("result", ""))
                    if "已保存到:" in result_text:
                        file_path = result_text.split("已保存到:")[-1].strip()
                        generated_files.append(file_path)

            return {
                "success": True,
                "message": summary,
                "steps_executed": len(execution_results),
                "results": execution_results,
                "generated_files": generated_files
            }

        except Exception as e:
            self.node.get_logger().error(f"AI Agent工作流执行异常: {e}")
            import traceback
            self.node.get_logger().error(f"异常堆栈: {traceback.format_exc()}")
            return {"success": False, "message": f"工作流执行失败: {str(e)}"}
    
    def _plan_task(self, task_description: str) -> Dict[str, Any]:
        """
        使用大模型进行任务规划和分解
        """
        try:
            self.node.get_logger().info("开始构造规划prompt")
            planning_prompt = f"""
作为一个智能Agent，请将以下任务分解为具体的执行步骤：

任务：{task_description}

可用工具：
- seewhat(): 获取摄像头图像，用于环境观察
- generate_image(prompt): 生成图片和视觉艺术作品
- write_document(format, title, content, filename): 生成和保存文档
- analyze_video(video_path): 分析视频文件
- visual_positioning(image_path, object_name): 视觉定位特定物体
- scan_table(image_path): 扫描表格内容

请按以下JSON格式返回分解步骤，只返回JSON，不要使用markdown格式包装：
{{
    "steps": [
        {{
            "id": 1,
            "description": "步骤描述",
            "tool": "工具名称",
            "parameters": {{"参数名": "参数值"}},
            "expected_outcome": "预期结果"
        }}
    ]
}}

重要限制和要求：
1. 严格按照工具描述中的用途使用工具，注意工具边界限制
2. 每个步骤只使用一个工具，不要混合使用
3. 工具间数据会自动传递，无需手动指定传递逻辑
4. 参数要具体明确，不要使用模糊描述或占位符
5. 步骤顺序要符合逻辑：观察→生成→记录
6. 只返回JSON格式，不要使用```json```包装
"""
            
            # 调用大模型进行规划
            self.node.get_logger().info("开始调用LLM进行任务规划")
            messages_to_use = [{"role": "user", "content": planning_prompt}]
            result = self.node.model_client.infer_with_text("", message=messages_to_use)

            if isinstance(result, dict):
                response_text = result.get('response', '')
            else:
                response_text = str(result)

            # 处理markdown格式的JSON响应
            json_text = response_text
            if "```json" in response_text:
                # 提取markdown中的JSON部分
                start_marker = "```json"
                end_marker = "```"
                start_idx = response_text.find(start_marker)
                if start_idx != -1:
                    start_idx += len(start_marker)
                    end_idx = response_text.find(end_marker, start_idx)
                    if end_idx != -1:
                        json_text = response_text[start_idx:end_idx].strip()
                        self.node.get_logger().info(f"提取的JSON文本: {json_text}")

            # 解析JSON响应
            try:
                plan_data = json.loads(json_text)
                steps = plan_data.get("steps", [])

                self.node.get_logger().info(f"解析得到的步骤: {steps}")

                if not steps:
                    self.node.get_logger().error("任务规划失败：未生成有效步骤")
                    return {"success": False, "message": "任务规划失败：未生成有效步骤"}

                return {"success": True, "steps": steps}

            except json.JSONDecodeError as e:
                # 如果JSON解析失败，尝试提取步骤信息
                self.node.get_logger().error(f"JSON解析失败: {e}, 响应内容: {response_text}")
                return {"success": False, "message": f"任务规划失败：响应格式错误 - {str(e)}"}

        except Exception as e:
            self.node.get_logger().error(f"任务规划异常: {e}")
            return {"success": False, "message": f"任务规划失败: {str(e)}"}

    def _process_step_parameters(self, step: Dict[str, Any], step_outputs: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理步骤参数中的占位符，替换为实际的文件路径
        """
        processed_step = step.copy()
        parameters = step.get("parameters", {}).copy()

        # 收集需要修改的参数
        replacements = {}
        keys_to_remove = []

        # 处理常见的占位符
        for key, value in parameters.items():
            if isinstance(value, str):
                # 替换各种占位符为实际的图片路径
                if value in ["step1_output_image_path", "step1_image.jpg"]:
                    # seewhat工具保存的图片路径
                    actual_path = "/home/<USER>/yahboom_ws/install/largemodel/share/largemodel/resources_file/captured_frame.jpg"
                    replacements[key] = actual_path
                    self.node.get_logger().info(f"替换参数 {key}: {value} → {actual_path}")

        # 处理analyze_video工具的参数名称不匹配问题
        if step.get("tool") == "analyze_video" and "path" in parameters:
            # analyze_video期望video_path参数
            replacements["video_path"] = parameters["path"]
            keys_to_remove.append("path")
            self.node.get_logger().info(f"修正analyze_video参数: path → video_path")

        # write_document工具不需要特殊的参数处理，LLM应该在content中包含所有需要的内容

        # 应用所有修改
        for key, value in replacements.items():
            parameters[key] = value

        for key in keys_to_remove:
            if key in parameters:
                del parameters[key]

        processed_step["parameters"] = parameters
        return processed_step

    def _execute_step(self, step: Dict[str, Any], previous_tool_outputs: Optional[List[Any]] = None) -> Dict[str, Any]:
        """
        执行单个任务步骤 - 使用工具链支持数据传递
        """
        try:
            tool_name = step.get("tool")
            parameters = step.get("parameters", {})
            description = step.get("description", "")

            # 确保previous_tool_outputs不为None
            if previous_tool_outputs is None:
                previous_tool_outputs = []

            self.node.get_logger().info(f"执行工具: {tool_name}, 参数: {parameters}")
            if previous_tool_outputs:
                self.node.get_logger().info(f"📋 可用的前置工具输出: {[output.tool_name for output in previous_tool_outputs]}")

            # 构造工具调用
            tool_call = {
                "name": tool_name,
                "arguments": parameters
            }

            # ✅ 修改：使用工具链执行，支持数据传递
            if hasattr(self.tools_manager, 'tool_chain_manager'):
                self.node.get_logger().info(f"🔗 使用工具链管理器执行工具: {tool_name}")

                # ✅ 关键修改：手动创建ToolInput来传递前置输出
                from utils.tool_chain_manager import ToolInput, ToolOutput

                # 创建工具输入，包含前置工具的输出
                tool_input = ToolInput(
                    arguments=parameters,
                    previous_outputs=previous_tool_outputs,
                    context={}
                )

                # 获取工具适配器并执行
                tool_adapter = self.tools_manager.tool_chain_manager.tools.get(tool_name)
                if tool_adapter:
                    # 使用工具链管理器的增强机制
                    enhanced_input = self.tools_manager.tool_chain_manager._enhance_tool_input(tool_input, tool_name)

                    # 直接执行工具适配器
                    output = tool_adapter.execute(enhanced_input)

                    if output.success:
                        tool_result = output.data
                        self.node.get_logger().info(f"✅ 工具链执行成功: {tool_name}")
                    else:
                        error_msg = output.error_message or "Tool execution failed"
                        self.node.get_logger().error(f"❌ 工具链执行失败: {error_msg}")
                        tool_result = f"工具执行失败: {error_msg}"
                else:
                    # 回退到工具链管理器的execute_tool_chain方法
                    outputs = self.tools_manager.tool_chain_manager.execute_tool_chain([tool_call])
                    if outputs and outputs[0].success:
                        tool_result = outputs[0].data
                        self.node.get_logger().info(f"✅ 工具链执行成功: {tool_name}")
                    else:
                        error_msg = outputs[0].error_message if outputs else "No output from tool chain"
                        self.node.get_logger().error(f"❌ 工具链执行失败: {error_msg}")
                        tool_result = f"工具执行失败: {error_msg}"
            else:
                # 回退到原有方式
                self.node.get_logger().warning("⚠️ 工具链管理器不可用，回退到传统执行方式")
                tool_result = self.tools_manager.execute_tool(tool_call, allow_follow_up_tools=True)

            return {
                "success": True,
                "step_id": step.get("id"),
                "description": description,
                "tool_used": tool_name,
                "result": tool_result,
                "message": f"步骤执行成功: {description}"
            }

        except Exception as e:
            return {
                "success": False,
                "step_id": step.get("id"),
                "description": step.get("description", ""),
                "error": str(e),
                "message": f"步骤执行失败: {str(e)}"
            }
    
    def _handle_step_failure(self, step: Dict[str, Any], step_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理步骤执行失败的情况
        """
        try:
            # 简单的重试机制
            self.node.get_logger().warn(f"步骤失败，尝试重试: {step['description']}")
            
            # 等待后重试
            time.sleep(self.retry_interval)
            retry_result = self._execute_step(step)
            
            if retry_result["success"]:
                retry_result["message"] += " (重试成功)"
                return retry_result
            else:
                return {
                    "success": False,
                    "message": f"步骤重试后仍然失败: {step['description']}"
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": f"步骤修复失败: {str(e)}"
            }
    
    def _should_continue_after_failure(self, step: Dict[str, Any], step_result: Dict[str, Any]) -> bool:
        """
        判断在步骤失败后是否应该继续执行
        """
        # 简单策略：对于非关键步骤，继续执行
        critical_tools = ["seewhat"]  # 定义关键工具
        
        tool_name = step.get("tool", "")
        if tool_name in critical_tools:
            return False  # 关键步骤失败，停止执行
        else:
            return True   # 非关键步骤失败，继续执行
    
    def _summarize_execution(self, task_description: str, execution_results: List[Dict[str, Any]]) -> str:
        """
        总结任务执行结果
        """
        successful_steps = [r for r in execution_results if r.get("success", False)]
        failed_steps = [r for r in execution_results if not r.get("success", False)]
        
        summary = f"任务'{task_description}'执行完成。"
        summary += f"共执行{len(execution_results)}个步骤，"
        summary += f"成功{len(successful_steps)}个，失败{len(failed_steps)}个。"
        
        if failed_steps:
            summary += f"失败的步骤：{', '.join([s.get('description', '') for s in failed_steps])}"
        
        return summary
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取Agent当前状态
        """
        successful_tasks = len([t for t in self.execution_history if t.get("success", False)])
        total_tasks = len(self.execution_history)

        return {
            "current_task": self.current_task,
            "total_tasks": total_tasks,
            "successful_tasks": successful_tasks,
            "success_rate": f"{(successful_tasks/total_tasks*100):.1f}%" if total_tasks > 0 else "0%",
            "recent_tasks": self.execution_history[-3:] if self.execution_history else []
        }
