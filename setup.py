from setuptools import find_packages, setup
import os
from glob import glob
package_name = 'largemodel'

setup(
    name=package_name,
    version='0.0.0',
    packages=find_packages(exclude=['test']),
    data_files=[
        ('share/ament_index/resource_index/packages',
            ['resource/' + package_name]),
        ('share/' + package_name, ['package.xml']),
        (os.path.join('share', package_name, 'launch'), glob('launch/*.launch.py')),
        (os.path.join('share', package_name, 'config'), glob('config/*.yaml')),
        (os.path.join('share', package_name, 'resources_file'), [os.path.join(root, f) for root, dirs, files in os.walk('resources_file') for f in files]),
    ],
    install_requires=['setuptools','ollama'],
    zip_safe=True,
    maintainer='jetson',
    maintainer_email='<EMAIL>',
    description='TODO: Package description',
    license='TODO: License declaration',
    tests_require=['pytest'],
    entry_points={
        'console_scripts': [
            'asr = largemodel.asr:main',
            'model_service = largemodel.model_service:main',   
            'tts_only = largemodel.tts_only:main',
        ],
    },
)
