# 远程MCP服务器配置文件
# Remote MCP Servers Configuration

# 示例：天气服务MCP服务器
weather_service:
  enabled: false  # 设为true启用
  type: "http"
  url: "https://weather-mcp.example.com/api"
  auth:
    type: "bearer"
    token: "your-weather-api-key"
  description: "Weather information and forecasting tools"

# 示例：翻译服务MCP服务器  
translation_service:
  enabled: false
  type: "http"
  url: "https://translate-mcp.example.com/api"
  auth:
    type: "api_key"
    key: "your-translation-api-key"
  description: "Multi-language translation tools"

# 示例：知识库服务MCP服务器
knowledge_base:
  enabled: false
  type: "websocket"
  url: "wss://kb-mcp.example.com/ws"
  auth:
    type: "bearer"
    token: "your-kb-api-key"
  description: "Knowledge base search and retrieval tools"

# 示例：本地服务（如果你运行了其他MCP服务器）
local_tools:
  enabled: false
  type: "http"
  url: "http://localhost:8080/mcp"
  auth:
    type: "none"
  description: "Local MCP tools server"

# 配置说明：
# enabled: 是否启用该服务器
# type: 连接类型 ("http" 或 "websocket")
# url: 服务器地址
# auth: 认证配置
#   - type: 认证类型 ("bearer", "api_key", "none")
#   - token/key: 认证凭据
# description: 服务器描述

# ========================================
# 内置AI服务适配器 (无需配置，自动可用)
# ========================================
# 以下工具已内置，可直接使用：
#
# 通义千问适配器：
# - tongyi.chat(message, model): 与通义千问模型对话
#   示例: "使用tongyi.chat帮我写一首诗"
# - tongyi.image_gen(prompt, size): 使用通义万相生成图片
#   示例: "用tongyi.image_gen生成一张风景画"
#
# OpenRouter适配器：
# - openrouter.chat(message, model): 通过OpenRouter调用各种AI模型
#   示例: "用openrouter.chat调用Claude模型分析这个问题"
#
# 这些适配器将AI服务包装成MCP工具，让你可以：
# 1. 在对话中直接调用不同的AI模型
# 2. 让AI助手使用其他AI服务作为工具
# 3. 实现多模型协作和对比
