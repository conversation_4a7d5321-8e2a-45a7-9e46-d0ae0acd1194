#!/usr/bin/env python3
"""
MCP (Model Context Protocol) 客户端实现
用于连接远程MCP服务器
"""

import json
import requests
import websocket
import threading
import time
from typing import Dict, List, Any, Optional
from dataclasses import dataclass


@dataclass
class RemoteTool:
    """远程工具定义"""
    name: str
    description: str
    input_schema: Dict[str, Any]
    server_url: str


class MCPClient:
    """
    MCP客户端 - 连接远程MCP服务器和AI服务适配器
    """

    def __init__(self, node):
        self.node = node
        self.remote_servers = {}  # 存储远程服务器连接
        self.remote_tools = {}    # 存储远程工具
        self.ai_adapters = {}     # 存储AI服务适配器
        self.request_id = 0       # 请求ID计数器

        # 初始化内置的AI服务适配器
        self._init_ai_adapters()

        self.node.get_logger().info("MCP Client initialized")

    def _init_ai_adapters(self):
        """初始化AI服务适配器"""
        # 通义千问适配器
        self.ai_adapters['tongyi'] = {
            'name': 'tongyi',
            'description': 'Tongyi Qianwen AI Service Adapter',
            'tools': {
                'tongyi.chat': {
                    'name': 'tongyi.chat',
                    'description': 'Chat with Tongyi Qianwen model',
                    'input_schema': {
                        'type': 'object',
                        'properties': {
                            'message': {'type': 'string', 'description': 'Message to send'},
                            'model': {'type': 'string', 'description': 'Model to use', 'default': 'qwen-turbo'}
                        },
                        'required': ['message']
                    }
                },
                'tongyi.image_gen': {
                    'name': 'tongyi.image_gen',
                    'description': 'Generate images using Tongyi Wanxiang',
                    'input_schema': {
                        'type': 'object',
                        'properties': {
                            'prompt': {'type': 'string', 'description': 'Image generation prompt'},
                            'size': {'type': 'string', 'description': 'Image size', 'default': '1024*1024'}
                        },
                        'required': ['prompt']
                    }
                }
            }
        }

        # OpenRouter适配器
        self.ai_adapters['openrouter'] = {
            'name': 'openrouter',
            'description': 'OpenRouter AI Service Adapter',
            'tools': {
                'openrouter.chat': {
                    'name': 'openrouter.chat',
                    'description': 'Chat with models via OpenRouter',
                    'input_schema': {
                        'type': 'object',
                        'properties': {
                            'message': {'type': 'string', 'description': 'Message to send'},
                            'model': {'type': 'string', 'description': 'Model to use', 'default': 'anthropic/claude-3-sonnet'}
                        },
                        'required': ['message']
                    }
                }
            }
        }

        # 将适配器工具添加到远程工具列表
        for adapter_name, adapter in self.ai_adapters.items():
            for tool_name, tool_config in adapter['tools'].items():
                self.remote_tools[tool_name] = RemoteTool(
                    name=tool_name,
                    description=tool_config['description'],
                    input_schema=tool_config['input_schema'],
                    server_url=f'adapter://{adapter_name}'
                )

        self.node.get_logger().info(f"Initialized {len(self.ai_adapters)} AI service adapters")

    def add_remote_server(self, server_name: str, server_config: Dict[str, Any]) -> bool:
        """
        添加远程MCP服务器
        
        Args:
            server_name: 服务器名称
            server_config: 服务器配置
                {
                    "url": "https://api.example.com/mcp",
                    "type": "http",  # 或 "websocket"
                    "auth": {
                        "type": "bearer",
                        "token": "your-api-key"
                    }
                }
        """
        try:
            if server_config["type"] == "http":
                success = self._connect_http_server(server_name, server_config)
            elif server_config["type"] == "websocket":
                success = self._connect_websocket_server(server_name, server_config)
            else:
                self.node.get_logger().error(f"Unsupported server type: {server_config['type']}")
                return False
            
            if success:
                self.remote_servers[server_name] = server_config
                self.node.get_logger().info(f"Successfully connected to remote server: {server_name}")
                
                # 获取远程工具列表
                self._fetch_remote_tools(server_name)
                return True
            else:
                return False
                
        except Exception as e:
            self.node.get_logger().error(f"Failed to connect to remote server {server_name}: {e}")
            return False
    
    def _connect_http_server(self, server_name: str, config: Dict[str, Any]) -> bool:
        """连接HTTP类型的MCP服务器"""
        try:
            # 发送初始化请求
            init_request = {
                "jsonrpc": "2.0",
                "method": "initialize",
                "params": {
                    "protocolVersion": "2024-11-05",
                    "capabilities": {},
                    "clientInfo": {
                        "name": "YahBoom Robot MCP Client",
                        "version": "1.0.0"
                    }
                },
                "id": self._get_next_id()
            }
            
            headers = {"Content-Type": "application/json"}
            
            # 添加认证头
            if "auth" in config:
                auth = config["auth"]
                if auth["type"] == "bearer":
                    headers["Authorization"] = f"Bearer {auth['token']}"
                elif auth["type"] == "api_key":
                    headers["X-API-Key"] = auth["key"]
            
            response = requests.post(
                config["url"],
                json=init_request,
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                if "error" not in result:
                    self.node.get_logger().info(f"HTTP server {server_name} initialized successfully")
                    return True
                else:
                    self.node.get_logger().error(f"Server initialization error: {result['error']}")
                    return False
            else:
                self.node.get_logger().error(f"HTTP connection failed: {response.status_code}")
                return False
                
        except Exception as e:
            self.node.get_logger().error(f"HTTP connection error: {e}")
            return False
    
    def _connect_websocket_server(self, server_name: str, config: Dict[str, Any]) -> bool:
        """连接WebSocket类型的MCP服务器"""
        try:
            # WebSocket连接实现
            # 这里简化实现，实际项目中需要完整的WebSocket处理
            self.node.get_logger().info(f"WebSocket connection to {server_name} - simplified implementation")
            return True
            
        except Exception as e:
            self.node.get_logger().error(f"WebSocket connection error: {e}")
            return False
    
    def _fetch_remote_tools(self, server_name: str):
        """获取远程服务器的工具列表"""
        try:
            config = self.remote_servers[server_name]
            
            if config["type"] == "http":
                tools_request = {
                    "jsonrpc": "2.0",
                    "method": "tools/list",
                    "params": {},
                    "id": self._get_next_id()
                }
                
                headers = {"Content-Type": "application/json"}
                if "auth" in config:
                    auth = config["auth"]
                    if auth["type"] == "bearer":
                        headers["Authorization"] = f"Bearer {auth['token']}"
                
                response = requests.post(
                    config["url"],
                    json=tools_request,
                    headers=headers,
                    timeout=10
                )
                
                if response.status_code == 200:
                    result = response.json()
                    if "result" in result and "tools" in result["result"]:
                        tools = result["result"]["tools"]
                        
                        for tool in tools:
                            tool_name = f"{server_name}.{tool['name']}"  # 添加服务器前缀
                            self.remote_tools[tool_name] = RemoteTool(
                                name=tool["name"],
                                description=tool["description"],
                                input_schema=tool.get("inputSchema", {}),
                                server_url=config["url"]
                            )
                        
                        self.node.get_logger().info(f"Fetched {len(tools)} tools from {server_name}")
                    else:
                        self.node.get_logger().warn(f"No tools found on server {server_name}")
                else:
                    self.node.get_logger().error(f"Failed to fetch tools: {response.status_code}")
                    
        except Exception as e:
            self.node.get_logger().error(f"Error fetching remote tools: {e}")
    
    def call_remote_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Optional[str]:
        """
        调用远程工具或AI适配器

        Args:
            tool_name: 工具名称（格式：server_name.tool_name）
            arguments: 工具参数

        Returns:
            工具执行结果
        """
        try:
            if tool_name not in self.remote_tools:
                self.node.get_logger().error(f"Remote tool not found: {tool_name}")
                return None

            remote_tool = self.remote_tools[tool_name]
            server_name = tool_name.split('.')[0]

            # 检查是否是AI适配器
            if remote_tool.server_url.startswith('adapter://'):
                adapter_name = remote_tool.server_url.replace('adapter://', '')
                return self._call_ai_adapter(adapter_name, tool_name, arguments)

            # 处理标准MCP服务器
            if server_name not in self.remote_servers:
                self.node.get_logger().error(f"Remote server not found: {server_name}")
                return None

            config = self.remote_servers[server_name]
            
            # 构造工具调用请求
            tool_request = {
                "jsonrpc": "2.0",
                "method": "tools/call",
                "params": {
                    "name": remote_tool.name,
                    "arguments": arguments
                },
                "id": self._get_next_id()
            }
            
            if config["type"] == "http":
                headers = {"Content-Type": "application/json"}
                if "auth" in config:
                    auth = config["auth"]
                    if auth["type"] == "bearer":
                        headers["Authorization"] = f"Bearer {auth['token']}"
                
                response = requests.post(
                    config["url"],
                    json=tool_request,
                    headers=headers,
                    timeout=30  # 工具调用可能需要更长时间
                )
                
                if response.status_code == 200:
                    result = response.json()
                    if "result" in result:
                        # 提取工具执行结果
                        content = result["result"].get("content", [])
                        if content and len(content) > 0:
                            return content[0].get("text", "Tool executed successfully")
                        else:
                            return "Tool executed successfully"
                    else:
                        error = result.get("error", {})
                        self.node.get_logger().error(f"Remote tool error: {error}")
                        return None
                else:
                    self.node.get_logger().error(f"Remote tool call failed: {response.status_code}")
                    return None
            
        except Exception as e:
            self.node.get_logger().error(f"Error calling remote tool: {e}")
            return None

    def _call_ai_adapter(self, adapter_name: str, tool_name: str, arguments: Dict[str, Any]) -> Optional[str]:
        """调用AI服务适配器"""
        try:
            self.node.get_logger().info(f"Calling AI adapter: {adapter_name}.{tool_name}")

            if adapter_name == 'tongyi':
                return self._call_tongyi_adapter(tool_name, arguments)
            elif adapter_name == 'openrouter':
                return self._call_openrouter_adapter(tool_name, arguments)
            else:
                self.node.get_logger().error(f"Unknown AI adapter: {adapter_name}")
                return None

        except Exception as e:
            self.node.get_logger().error(f"Error calling AI adapter {adapter_name}: {e}")
            return None

    def _call_tongyi_adapter(self, tool_name: str, arguments: Dict[str, Any]) -> Optional[str]:
        """调用通义千问适配器"""
        try:
            if tool_name == 'tongyi.chat':
                # 调用通义千问聊天API
                message = arguments.get('message', '')
                model = arguments.get('model', 'qwen-turbo')

                # 使用现有的model_client进行调用
                if hasattr(self.node, 'model_client'):
                    response = self.node.model_client.chat_with_model(message, model)
                    return f"通义千问回复: {response}"
                else:
                    return "错误: 无法访问model_client"

            elif tool_name == 'tongyi.image_gen':
                # 调用通义万相图片生成API
                prompt = arguments.get('prompt', '')
                size = arguments.get('size', '1024*1024')

                if hasattr(self.node, 'model_client'):
                    result = self.node.model_client.text_to_image(prompt)
                    if result and result.get('status') == 'success':
                        return f"图片生成成功: {result.get('image_urls', [])}"
                    else:
                        return f"图片生成失败: {result.get('error', '未知错误')}"
                else:
                    return "错误: 无法访问model_client"
            else:
                return f"未知的通义千问工具: {tool_name}"

        except Exception as e:
            self.node.get_logger().error(f"Error in Tongyi adapter: {e}")
            return f"通义千问适配器错误: {e}"

    def _call_openrouter_adapter(self, tool_name: str, arguments: Dict[str, Any]) -> Optional[str]:
        """调用OpenRouter适配器"""
        try:
            if tool_name == 'openrouter.chat':
                message = arguments.get('message', '')
                model = arguments.get('model', 'anthropic/claude-3-sonnet')

                # 这里可以实现OpenRouter的API调用
                # 由于你目前主要使用通义千问，这里先返回一个占位符
                return f"OpenRouter调用 (模型: {model}): {message} - 功能待实现"
            else:
                return f"未知的OpenRouter工具: {tool_name}"

        except Exception as e:
            self.node.get_logger().error(f"Error in OpenRouter adapter: {e}")
            return f"OpenRouter适配器错误: {e}"

    def get_all_tools(self) -> Dict[str, RemoteTool]:
        """获取所有远程工具"""
        return self.remote_tools.copy()
    
    def get_tools_for_prompt(self) -> str:
        """
        为LLM生成远程工具描述
        """
        if not self.remote_tools:
            return ""
        
        tools_description = "Remote Tools (Available via MCP):\n"
        
        for tool_name, tool in self.remote_tools.items():
            # 从schema生成参数描述
            params_desc = ""
            if tool.input_schema.get("properties"):
                params = []
                for param_name, param_info in tool.input_schema["properties"].items():
                    params.append(f"{param_name}: {param_info.get('description', param_info.get('type', 'string'))}")
                if params:
                    params_desc = f"({', '.join(params)})"
            
            tools_description += f"- {tool_name}{params_desc}: {tool.description}\n"
        
        return tools_description
    
    def _get_next_id(self) -> str:
        """生成下一个请求ID"""
        self.request_id += 1
        return str(self.request_id)
    
    def disconnect_all(self):
        """断开所有远程连接"""
        self.remote_servers.clear()
        self.remote_tools.clear()
        self.node.get_logger().info("All remote connections disconnected")
