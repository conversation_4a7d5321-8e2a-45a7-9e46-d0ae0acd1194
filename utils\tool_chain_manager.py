"""
工具链数据传递管理器
实现通用的工具间数据流转机制
"""

from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass
from abc import ABC, abstractmethod
import json
import logging

@dataclass
class ToolOutput:
    """标准化的工具输出格式"""
    tool_name: str
    success: bool
    data: Any  # 主要输出数据
    metadata: Dict[str, Any]  # 元数据（如文件路径、坐标等）
    error_message: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "tool_name": self.tool_name,
            "success": self.success,
            "data": self.data,
            "metadata": self.metadata,
            "error_message": self.error_message
        }

@dataclass
class ToolInput:
    """标准化的工具输入格式"""
    arguments: Dict[str, Any]  # 原始参数
    previous_outputs: List[ToolOutput]  # 前置工具的输出
    context: Dict[str, Any]  # 全局上下文
    
    def get_previous_output_by_tool(self, tool_name: str) -> Optional[ToolOutput]:
        """根据工具名获取前置输出"""
        for output in self.previous_outputs:
            if output.tool_name == tool_name:
                return output
        return None
    
    def get_latest_output(self) -> Optional[ToolOutput]:
        """获取最新的工具输出"""
        return self.previous_outputs[-1] if self.previous_outputs else None

class ToolInterface(ABC):
    """工具接口基类"""
    
    @property
    @abstractmethod
    def tool_name(self) -> str:
        """工具名称"""
        pass
    
    @property
    @abstractmethod
    def input_schema(self) -> Dict[str, Any]:
        """输入参数模式定义"""
        pass
    
    @property
    @abstractmethod
    def output_schema(self) -> Dict[str, Any]:
        """输出数据模式定义"""
        pass
    
    @abstractmethod
    def execute(self, tool_input: ToolInput) -> ToolOutput:
        """执行工具逻辑"""
        pass
    
    def can_use_previous_output(self, previous_output: ToolOutput) -> bool:
        """判断是否可以使用前置工具的输出"""
        return False  # 默认不使用，子类可重写

class ToolChainManager:
    """工具链数据传递管理器"""
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)
        self.tools: Dict[str, ToolInterface] = {}

    def register_tool(self, tool: ToolInterface):
        """注册工具"""
        self.tools[tool.tool_name] = tool
        self.logger.info(f"注册工具: {tool.tool_name}")
    
    def execute_tool_chain(self, tool_calls: List[Dict[str, Any]]) -> List[ToolOutput]:
        """执行工具链"""
        outputs = []
        context = {}
        
        for i, tool_call in enumerate(tool_calls):
            tool_name = tool_call.get("name")
            arguments = tool_call.get("arguments", {})
            
            if tool_name not in self.tools:
                self.logger.error(f"未找到工具: {tool_name}")
                continue
            
            # 创建工具输入
            tool_input = ToolInput(
                arguments=arguments,
                previous_outputs=outputs.copy(),
                context=context
            )
            
            # 自动参数增强
            enhanced_input = self._enhance_tool_input(tool_input, tool_name)
            
            # 执行工具
            try:
                tool = self.tools[tool_name]
                output = tool.execute(enhanced_input)
                outputs.append(output)
                
                # 更新上下文
                context[f"{tool_name}_result"] = output.data
                
                self.logger.info(f"工具 {tool_name} 执行完成: {output.success}")
                
            except Exception as e:
                error_output = ToolOutput(
                    tool_name=tool_name,
                    success=False,
                    data=None,
                    metadata={},
                    error_message=str(e)
                )
                outputs.append(error_output)
                self.logger.error(f"工具 {tool_name} 执行失败: {e}")
        
        return outputs
    
    def _enhance_tool_input(self, tool_input: ToolInput, current_tool: str) -> ToolInput:
        """自动增强工具输入参数"""
        enhanced_arguments = tool_input.arguments.copy()
        
        # ✅ 使用智能通用数据传递机制
        if tool_input.previous_outputs:
            enhanced_arguments = self._apply_generic_enhancements(
                enhanced_arguments, tool_input.previous_outputs, current_tool
            )
        
        return ToolInput(
            arguments=enhanced_arguments,
            previous_outputs=tool_input.previous_outputs,
            context=tool_input.context
        )
    
    def _apply_generic_enhancements(self, arguments: Dict[str, Any],
                                   previous_outputs: List[ToolOutput],
                                   current_tool: str) -> Dict[str, Any]:
        """应用通用的参数增强逻辑"""
        enhanced = arguments.copy()

        # 🔧 新增：通用数据传递机制
        enhanced = self._apply_universal_data_passing(enhanced, previous_outputs, current_tool)

        # 保留原有的特定增强逻辑作为补充
        enhanced = self._apply_specific_enhancements(enhanced, previous_outputs, current_tool)

        return enhanced

    def _apply_universal_data_passing(self, arguments: Dict[str, Any],
                                    previous_outputs: List[ToolOutput],
                                    current_tool: str) -> Dict[str, Any]:
        """🔧 通用数据传递机制：基于数据类型和语义自动匹配"""
        enhanced = arguments.copy()

        if not previous_outputs:
            return enhanced

        # 获取当前工具的输入需求
        current_tool_interface = self.tools.get(current_tool)
        if not current_tool_interface:
            return enhanced

        input_schema = current_tool_interface.input_schema
        required_params = input_schema.get("properties", {})

        # 遍历所有参数，尝试自动填充
        for param_name, param_schema in required_params.items():
            # ✅ 对于prompt参数，即使已有值也要尝试用更具体的描述替换
            should_skip = False
            if param_name in enhanced and enhanced[param_name]:
                if param_name == "prompt":
                    # 检查是否是通用描述，如果是则用具体描述替换
                    current_prompt = enhanced[param_name]
                    if any(generic in current_prompt for generic in ["根据", "类似", "基于", "similar", "based on"]):
                        self.logger.info(f"检测到通用prompt描述，尝试用具体描述替换")
                    else:
                        should_skip = True
                else:
                    should_skip = True

            if should_skip:
                continue

            # 🔧 基于参数语义自动匹配数据
            matched_value = self._find_matching_data(param_name, param_schema, previous_outputs)
            if matched_value is not None:
                enhanced[param_name] = matched_value
                self.logger.info(f"自动填充参数 {param_name}: {str(matched_value)[:50]}...")

        return enhanced

    def _find_matching_data(self, param_name: str, param_schema: Dict[str, Any],
                           previous_outputs: List[ToolOutput]) -> Any:
        """🔧 基于参数名称和类型智能匹配前置工具的输出数据"""

        # 🚨 排除不应该自动匹配的参数
        excluded_params = ["filename", "title", "format"]  # 这些参数应该由LLM或工具自己决定
        if param_name in excluded_params:
            return None

        # 1. 基于参数名称的语义匹配
        semantic_mappings = {
            # 图像相关参数
            "image_path": ["image_path", "captured_image", "photo_path"],
            "image_url": ["image_url", "image_path"],

            # 内容相关参数
            "content": ["description", "analysis", "result", "summary"],
            "text": ["description", "analysis", "result", "content"],
            "description": ["description", "analysis", "summary"],

            # 提示词相关参数
            "prompt": ["description", "content", "summary"],

            # 文件路径相关参数
            "file_path": ["file_path", "output_path", "saved_path"],
            "video_path": ["video_path", "media_path"],
        }

        # 2. 遍历前置输出，寻找匹配的数据
        for output in reversed(previous_outputs):  # 从最新的输出开始查找
            if not output.success:
                continue

            # 2.1 检查主数据
            if param_name in semantic_mappings:
                for semantic_key in semantic_mappings[param_name]:
                    # 检查元数据中是否有匹配的键
                    if semantic_key in output.metadata:
                        return output.metadata[semantic_key]

                    # 检查主数据是否符合语义
                    if semantic_key in ["description", "analysis", "result", "summary", "content"]:
                        if output.data and isinstance(output.data, str):
                            return output.data

            # 2.2 直接参数名匹配
            if param_name in output.metadata:
                return output.metadata[param_name]

        # 3. 基于数据类型的通用匹配
        param_type = param_schema.get("type", "string")

        for output in reversed(previous_outputs):
            if not output.success:
                continue

            if param_type == "string" and isinstance(output.data, str):
                # 对于字符串类型，检查内容是否合适
                if self._is_suitable_string_data(param_name, output.data):
                    return output.data

            elif param_type == "object" and isinstance(output.data, dict):
                return output.data

        return None

    def _is_suitable_string_data(self, param_name: str, data: str) -> bool:
        """判断字符串数据是否适合作为指定参数的值"""
        if not data or len(data.strip()) < 10:
            return False

        # 基于参数名称判断数据适用性
        content_params = ["content", "description", "text", "summary", "prompt"]
        path_params = ["path", "file_path", "image_path", "video_path"]

        if param_name in content_params:
            # 内容类参数：检查是否像描述性文本
            return len(data) > 20 and not data.startswith("/") and "." not in data[:10]

        elif param_name in path_params:
            # 路径类参数：检查是否像文件路径
            return "/" in data or "\\" in data or data.endswith(('.jpg', '.png', '.mp4', '.txt', '.md'))

        return True

    def _apply_specific_enhancements(self, arguments: Dict[str, Any],
                                   previous_outputs: List[ToolOutput],
                                   current_tool: str) -> Dict[str, Any]:
        """应用特定的增强逻辑（简化版，移除硬编码模板）"""
        enhanced = arguments.copy()

        # 只保留必要的图像路径传递逻辑
        if current_tool in ["visual_positioning", "scan_table"] and "image_path" in enhanced:
            if enhanced["image_path"] in ["default", "current_image", "latest_image"]:
                seewhat_output = self._find_output_by_tool(previous_outputs, "seewhat")
                if seewhat_output and seewhat_output.metadata.get("image_path"):
                    enhanced["image_path"] = seewhat_output.metadata["image_path"]
                    self.logger.info("自动传递图像路径")

        # 🗑️ 删除硬编码的内容生成逻辑
        # 让工具适配器和LLM自己处理内容生成

        return enhanced
    
    def _find_output_by_tool(self, outputs: List[ToolOutput], tool_name: str) -> Optional[ToolOutput]:
        """查找指定工具的输出"""
        for output in reversed(outputs):  # 从最新的开始查找
            if output.tool_name == tool_name and output.success:
                return output
        return None

# 🗑️ 删除硬编码的数据映射器
# 原有的 seewhat_to_write_document_mapper 包含大量硬编码模板，违反LLM优先设计原则
# 现在通过通用数据传递机制和工具适配器的智能增强来处理数据传递

# 🔧 DEPRECATED: 以下映射器已被通用数据传递机制替代
# 大部分数据传递现在通过 _apply_universal_data_passing 自动处理
# 这些函数已被移除，因为通用机制能够自动处理相同的功能：
# - seewhat_to_visual_positioning_mapper: 图像路径自动传递
# - seewhat_to_scan_table_mapper: 图像路径自动传递
# - seewhat_to_generate_image_mapper: 提示词自动增强
# - analyze_video_to_write_document_mapper: 内容自动填充

# 如需特殊的数据传递规则，可以在这里添加新的映射器函数
