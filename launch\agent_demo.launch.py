#!/usr/bin/env python3

from launch import LaunchDescription
from launch_ros.actions import Node
from launch.actions import DeclareLaunchArgument, LogInfo
from launch.substitutions import LaunchConfiguration
from ament_index_python.packages import get_package_share_directory
import os

def generate_launch_description():
    """
    AI Agent 演示启动文件
    """
    
    # 获取包路径
    pkg_share = get_package_share_directory('largemodel')
    
    # 声明启动参数
    language_arg = DeclareLaunchArgument(
        'language',
        default_value='zh',
        description='Language setting (zh/en)'
    )
    
    llm_platform_arg = DeclareLaunchArgument(
        'llm_platform',
        default_value='tongyi',
        description='LLM platform (ollama/tongyi/spark/qianfan/openrouter)'
    )
    
    use_online_tts_arg = DeclareLaunchArgument(
        'useolinetts',
        default_value='False',
        description='Use online TTS (True/False)'
    )
    
    # 配置文件路径
    config_file = os.path.join(pkg_share, 'config', 'yahboom.yaml')
    
    # 大模型服务节点
    model_service_node = Node(
        package='largemodel',
        executable='model_service',
        name='model_service',
        parameters=[
            config_file,
            {
                'language': LaunchConfiguration('language'),
                'llm_platform': LaunchConfiguration('llm_platform'),
                'useolinetts': LaunchConfiguration('useolinetts'),
                'text_chat_mode': False  # 启用语音模式以便完整体验Agent功能
            }
        ],
        output='screen'
    )
    
    # ASR节点
    asr_node = Node(
        package='largemodel',
        executable='asr',
        name='asr_node',
        parameters=[config_file],
        output='screen'
    )
    

    
    return LaunchDescription([
        # 启动参数
        language_arg,
        llm_platform_arg,
        use_online_tts_arg,
        
        # 启动信息
        LogInfo(msg='Starting AI Agent Demo...'),
        LogInfo(msg=['Language: ', LaunchConfiguration('language')]),
        LogInfo(msg=['LLM Platform: ', LaunchConfiguration('llm_platform')]),
        
        # 节点
        model_service_node,
        asr_node,
        
        LogInfo(msg='AI Agent Demo started successfully!')
    ])
